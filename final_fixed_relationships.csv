source_node,source_type,destination_node,destination_type,relationship,file_path,application,operation_context,class_context,method_context
ServiceBolt,application,ServiceBolt,folder,contains,,ServiceBolt,,,
ServiceBolt,folder,api,folder,contains,,ServiceBolt,,,
ServiceBolt,folder,service,folder,contains,,ServiceBolt,,,
UnifiedBolt,application,UnifiedBolt,folder,contains,,UnifiedBolt,,,
UnifiedBolt,folder,core,folder,contains,,UnifiedBolt,,,
core,folder,model,folder,contains,,UnifiedBolt,,,
core,folder,repository,folder,contains,,UnifiedBolt,,,
UnifiedBolt,folder,githubaction,folder,contains,,UnifiedBolt,,,
api,folder,BuildToolController.java,file,contains,ServiceBolt\api\BuildToolController.java,ServiceBolt,,,
service,folder,BuildToolService.java,file,contains,ServiceBolt\service\BuildToolService.java,ServiceBolt,,,
service,folder,BuildToolServiceImplemantation.java,file,contains,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt,,,
model,folder,BaseModel.java,file,contains,UnifiedBolt\core\model\BaseModel.java,UnifiedBolt,,,
model,folder,BuildFailurePatternForProjectInJenkinsModel.java,file,contains,UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java,UnifiedBolt,,,
model,folder,BuildFailurePatternMetrics.java,file,contains,UnifiedBolt\core\model\BuildFailurePatternMetrics.java,UnifiedBolt,,,
model,folder,BuildFileInfo.java,file,contains,UnifiedBolt\core\model\BuildFileInfo.java,UnifiedBolt,,,
model,folder,BuildInfo.java,file,contains,UnifiedBolt\core\model\BuildInfo.java,UnifiedBolt,,,
model,folder,BuildSteps.java,file,contains,UnifiedBolt\core\model\BuildSteps.java,UnifiedBolt,,,
model,folder,BuildTool.java,file,contains,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt,,,
model,folder,BuildToolMetric.java,file,contains,UnifiedBolt\core\model\BuildToolMetric.java,UnifiedBolt,,,
model,folder,ConfigurationSetting.java,file,contains,UnifiedBolt\core\model\ConfigurationSetting.java,UnifiedBolt,,,
model,folder,ConfigurationToolInfoMetric.java,file,contains,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt,,,
repository,folder,BuildFailurePatternForProjectRepo.java,file,contains,UnifiedBolt\core\repository\BuildFailurePatternForProjectRepo.java,UnifiedBolt,,,
repository,folder,BuildToolRep.java,file,contains,UnifiedBolt\core\repository\BuildToolRep.java,UnifiedBolt,,,
repository,folder,ConfigurationSettingRep.java,file,contains,UnifiedBolt\core\repository\ConfigurationSettingRep.java,UnifiedBolt,,,
githubaction,folder,GithubAction.java,file,contains,UnifiedBolt\githubaction\GithubAction.java,UnifiedBolt,,,
githubaction,folder,GithubActionApplication.java,file,contains,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt,,,
githubaction,folder,GithubActionImplementation.java,file,contains,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt,,,
OneInsights,project,ServiceBolt,application,contains,,ServiceBolt,,,
OneInsights,project,UnifiedBolt,application,contains,,UnifiedBolt,,,
buildtoolcontroller,class,get /buildjoblist,endpoint,exposes,C:\Shaik\sample\OneInsights\ServiceBolt\api\BuildToolController.java,ServiceBolt,,,
buildtoolcontroller,class,get /builddetailshome,endpoint,exposes,C:\Shaik\sample\OneInsights\ServiceBolt\api\BuildToolController.java,ServiceBolt,,,
buildtoolcontroller,class,get /builddetails,endpoint,exposes,C:\Shaik\sample\OneInsights\ServiceBolt\api\BuildToolController.java,ServiceBolt,,,
buildtoolcontroller,class,get /jobslist,endpoint,exposes,C:\Shaik\sample\OneInsights\ServiceBolt\api\BuildToolController.java,ServiceBolt,,,
buildtoolcontroller,class,get /jenkinsbuildfailure,endpoint,exposes,C:\Shaik\sample\OneInsights\ServiceBolt\api\BuildToolController.java,ServiceBolt,,,
buildtoolcontroller,class,get /jenkinsbuildfailuredata,endpoint,exposes,C:\Shaik\sample\OneInsights\ServiceBolt\api\BuildToolController.java,ServiceBolt,,,
buildtoolcontroller,class,get /jenkinsbuildfailurepatternfetchdata,endpoint,exposes,C:\Shaik\sample\OneInsights\ServiceBolt\api\BuildToolController.java,ServiceBolt,,,
buildtoolcontroller,class,get /jenkinsbuildfailurepatternfetchdataconfig,endpoint,exposes,C:\Shaik\sample\OneInsights\ServiceBolt\api\BuildToolController.java,ServiceBolt,,,
buildtoolcontroller,class,get /getbuildvaluestream,endpoint,exposes,C:\Shaik\sample\OneInsights\ServiceBolt\api\BuildToolController.java,ServiceBolt,,,
buildtoolcontroller,class,get /getgitlabbuildvaluestream,endpoint,exposes,C:\Shaik\sample\OneInsights\ServiceBolt\api\BuildToolController.java,ServiceBolt,,,
buildtoolcontroller,class,buildtoolservice,variable,has_field,C:\Shaik\sample\OneInsights\ServiceBolt\api\BuildToolController.java,ServiceBolt,,,
buildtoolcontroller,class,getonejob,method,declares,C:\Shaik\sample\OneInsights\ServiceBolt\api\BuildToolController.java,ServiceBolt,,,
buildtoolcontroller,class,getbuilddetailshome,method,declares,C:\Shaik\sample\OneInsights\ServiceBolt\api\BuildToolController.java,ServiceBolt,,,
buildtoolcontroller,class,builddata,method,declares,C:\Shaik\sample\OneInsights\ServiceBolt\api\BuildToolController.java,ServiceBolt,,,
buildtoolcontroller,class,joblist,method,declares,C:\Shaik\sample\OneInsights\ServiceBolt\api\BuildToolController.java,ServiceBolt,,,
buildtoolcontroller,class,savebuildfailurepattern,method,declares,C:\Shaik\sample\OneInsights\ServiceBolt\api\BuildToolController.java,ServiceBolt,,,
buildtoolcontroller,class,fetchdata,method,declares,C:\Shaik\sample\OneInsights\ServiceBolt\api\BuildToolController.java,ServiceBolt,,,
buildtoolcontroller,class,fetchfailurepatterndata,method,declares,C:\Shaik\sample\OneInsights\ServiceBolt\api\BuildToolController.java,ServiceBolt,,,
buildtoolcontroller,class,fetchfailurepatterndatacopy,method,declares,C:\Shaik\sample\OneInsights\ServiceBolt\api\BuildToolController.java,ServiceBolt,,,
buildtoolcontroller,class,gitlabbuilddata,method,declares,C:\Shaik\sample\OneInsights\ServiceBolt\api\BuildToolController.java,ServiceBolt,,,
buildtoolcontroller,class,getbuildtype,method,declares,C:\Shaik\sample\OneInsights\ServiceBolt\api\BuildToolController.java,ServiceBolt,,,
variable,variable,configurationrepo,variable,transforms_to,C:\Shaik\sample\OneInsights\ServiceBolt\api\BuildToolController.java,ServiceBolt,,,
getbean,variable,configurationrepo,variable,transforms_to,C:\Shaik\sample\OneInsights\ServiceBolt\api\BuildToolController.java,ServiceBolt,,,
configurationrepo,variable,configurationcolection,variable,transforms_to,C:\Shaik\sample\OneInsights\ServiceBolt\api\BuildToolController.java,ServiceBolt,,,
findbyprojectname,variable,configurationcolection,variable,transforms_to,C:\Shaik\sample\OneInsights\ServiceBolt\api\BuildToolController.java,ServiceBolt,,,
configurationcolection,variable,metric,variable,transforms_to,C:\Shaik\sample\OneInsights\ServiceBolt\api\BuildToolController.java,ServiceBolt,,,
getmetrics,variable,metric,variable,transforms_to,C:\Shaik\sample\OneInsights\ServiceBolt\api\BuildToolController.java,ServiceBolt,,,
BuildToolController.getBuildType.context,variable,BuildToolController.getBuildType.configurationRepo,variable,transforms_to,,,getBean,BuildToolController,getBuildType
BuildToolController.getBuildType.configurationRepo,variable,BuildToolController.getBuildType.configurationColection,variable,transforms_to,,,findByProjectName,BuildToolController,getBuildType
BuildToolController.getBuildType.configurationColection,variable,BuildToolController.getBuildType.metric,variable,transforms_to,,,getMetrics,BuildToolController,getBuildType
BuildToolController.getBuildType.AnnotationConfigApplicationContext,variable,BuildToolController.getBuildType.context,variable,transforms_to,,,assign,BuildToolController,getBuildType
BuildToolController.getBuildType.contex,variable,BuildToolController.getBuildType.configurationRepo,variable,transforms_to,,,assign,BuildToolController,getBuildType
BuildToolController.getBuildType.configurationRep,variable,BuildToolController.getBuildType.configurationColection,variable,transforms_to,,,assign,BuildToolController,getBuildType
BuildToolController.getBuildType.configurationColectio,variable,BuildToolController.getBuildType.metric,variable,transforms_to,,,assign,BuildToolController,getBuildType
context,variable,configurationrepo,variable,transforms_to,C:\Shaik\sample\OneInsights\ServiceBolt\api\BuildToolController.java,ServiceBolt,,,
buildtoolserviceimplemantation,file,buildtoolserviceimplemantation,class,declares,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt,,,
buildtoolserviceimplemantation,class,buildtoolservice,interface,implements,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt,,,
buildtoolservicerepository,variable,result,variable,transforms_to,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt,,,
buildtoolserviceimplemantation,class,buildtoolrepository,variable,has_field,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt,,,
buildtoolserviceimplemantation,class,buildfailurepatternforprojectrepo,variable,has_field,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt,,,
buildtoolserviceimplemantation,class,configsettingrepo,variable,has_field,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt,,,
buildtoolserviceimplemantation,class,metric,variable,has_field,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt,,,
buildtoolserviceimplemantation,class,nodataconst,variable,has_field,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt,,,
buildtoolserviceimplemantation,class,buildconst,variable,has_field,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt,,,
buildtoolserviceimplemantation,class,search,method,declares,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt,,,
buildtoolserviceimplemantation,class,searchjoblist,method,declares,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt,,,
buildtoolserviceimplemantation,class,searchfortest,method,declares,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt,,,
buildtoolserviceimplemantation,class,buildfailurepattern,method,declares,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt,,,
buildtoolserviceimplemantation,class,fetchbuilddata,method,declares,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt,,,
buildtoolserviceimplemantation,class,fetchfailurepatterndata,method,declares,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt,,,
buildtoolserviceimplemantation,class,getonebyprojectname,method,declares,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt,,,
buildtoolserviceimplemantation,class,getbuilddetailshome,method,declares,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt,,,
buildtoolserviceimplemantation,class,getvaluestream,method,declares,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt,,,
buildtoolserviceimplemantation,class,getgitlabvaluestream,method,declares,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt,,,
buildtoolrepository,variable,result,variable,transforms_to,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt,,,
findbyname,variable,result,variable,transforms_to,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt,,,
findbynameandtoolnameandtimestampbetween,variable,result,variable,transforms_to,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt,,,
findbynameandbuildtypeignorecase,variable,result,variable,transforms_to,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt,,,
findbybuildtypeandname,variable,result,variable,transforms_to,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt,,,
configsettingrepo,variable,config,variable,transforms_to,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt,,,
findbyprojectname,variable,config,variable,transforms_to,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt,,,
mongoaggr,variable,lastupdate,variable,transforms_to,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt,,,
getbuildscount,variable,lastupdate,variable,transforms_to,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt,,,
findbynameandtimestampbetween,variable,result,variable,transforms_to,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt,,,
buildfailurepatternforprojectrepo,variable,buildfailurepatternforprojectlist,variable,transforms_to,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt,,,
findbyprojectname,variable,buildfailurepatternforprojectlist,variable,transforms_to,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt,,,
buildfailurepatternforprojectrepo,variable,result,variable,transforms_to,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt,,,
findall,variable,result,variable,transforms_to,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt,,,
buildfailurepatternforprojectrepo,variable,response,variable,transforms_to,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt,,,
findbyprojectname,variable,response,variable,transforms_to,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt,,,
findonebynameorderbybuildiddesc,variable,result,variable,transforms_to,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt,,,
projhomecalc,variable,result,variable,transforms_to,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt,,,
getbuilddetailshome,variable,result,variable,transforms_to,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt,,,
BuildToolServiceImplemantation.search.buildToolRepository,variable,BuildToolServiceImplemantation.search.result,variable,transforms_to,,,findByName,BuildToolServiceImplemantation,search
BuildToolServiceImplemantation.search.buildToolRepositor,variable,BuildToolServiceImplemantation.search.result,variable,transforms_to,,,assign,BuildToolServiceImplemantation,search
BuildToolServiceImplemantation.searchJobList.buildToolRepository,variable,BuildToolServiceImplemantation.searchJobList.result,variable,transforms_to,,,findByBuildTypeAndName,BuildToolServiceImplemantation,searchJobList
BuildToolServiceImplemantation.searchJobList.buildToolRepositor,variable,BuildToolServiceImplemantation.searchJobList.result,variable,transforms_to,,,assign,BuildToolServiceImplemantation,searchJobList
BuildToolServiceImplemantation.searchForTest.buildToolRepository,variable,BuildToolServiceImplemantation.searchForTest.result,variable,transforms_to,,,findByBuildTypeAndName,BuildToolServiceImplemantation,searchForTest
BuildToolServiceImplemantation.searchForTest.buildToolRepositor,variable,BuildToolServiceImplemantation.searchForTest.result,variable,transforms_to,,,assign,BuildToolServiceImplemantation,searchForTest
BuildToolServiceImplemantation.search.configSettingRepo,variable,BuildToolServiceImplemantation.search.config,variable,transforms_to,,,findByProjectName,BuildToolServiceImplemantation,search
BuildToolServiceImplemantation.search.ConfigurationToolInfoMetric,variable,BuildToolServiceImplemantation.search.metric,variable,transforms_to,,,assign,BuildToolServiceImplemantation,search
BuildToolServiceImplemantation.search.configSettingRep,variable,BuildToolServiceImplemantation.search.config,variable,transforms_to,,,assign,BuildToolServiceImplemantation,search
BuildToolServiceImplemantation.buildFailurePattern.BuildFailurePatternForProjectInJenkinsModel,variable,BuildToolServiceImplemantation.buildFailurePattern.patternForproject,variable,transforms_to,,,assign,BuildToolServiceImplemantation,buildFailurePattern
BuildToolServiceImplemantation.fetchBuildData.buildFailurePatternForProjectRepo,variable,BuildToolServiceImplemantation.fetchBuildData.result,variable,transforms_to,,,findAll,BuildToolServiceImplemantation,fetchBuildData
BuildToolServiceImplemantation.fetchBuildData.buildFailurePatternForProjectRep,variable,BuildToolServiceImplemantation.fetchBuildData.result,variable,transforms_to,,,assign,BuildToolServiceImplemantation,fetchBuildData
BuildToolServiceImplemantation.fetchFailurePatternData.buildFailurePatternForProjectRepo,variable,BuildToolServiceImplemantation.fetchFailurePatternData.response,variable,transforms_to,,,findByProjectName,BuildToolServiceImplemantation,fetchFailurePatternData
BuildToolServiceImplemantation.fetchFailurePatternData.buildFailurePatternForProjectRep,variable,BuildToolServiceImplemantation.fetchFailurePatternData.response,variable,transforms_to,,,assign,BuildToolServiceImplemantation,fetchFailurePatternData
BuildToolServiceImplemantation.getOneByProjectName.buildToolRepository,variable,BuildToolServiceImplemantation.getOneByProjectName.result,variable,transforms_to,,,findOneByNameOrderByBuildIDDesc,BuildToolServiceImplemantation,getOneByProjectName
BuildToolServiceImplemantation.getOneByProjectName.buildToolRepositor,variable,BuildToolServiceImplemantation.getOneByProjectName.result,variable,transforms_to,,,assign,BuildToolServiceImplemantation,getOneByProjectName
BuildToolServiceImplemantation.getBuildDetailsHome.projHomeCalc,variable,BuildToolServiceImplemantation.getBuildDetailsHome.result,variable,transforms_to,,,getBuildDetailsHome,BuildToolServiceImplemantation,getBuildDetailsHome
BuildToolServiceImplemantation.getBuildDetailsHome.ProjectHomeCalculation,variable,BuildToolServiceImplemantation.getBuildDetailsHome.projHomeCalc,variable,transforms_to,,,assign,BuildToolServiceImplemantation,getBuildDetailsHome
BuildToolServiceImplemantation.getBuildDetailsHome.projHomeCal,variable,BuildToolServiceImplemantation.getBuildDetailsHome.result,variable,transforms_to,,,assign,BuildToolServiceImplemantation,getBuildDetailsHome
BuildToolServiceImplemantation.getValueStream.configSettingRepo,variable,BuildToolServiceImplemantation.getValueStream.config,variable,transforms_to,,,findByProjectName,BuildToolServiceImplemantation,getValueStream
BuildToolServiceImplemantation.getValueStream.ConfigurationToolInfoMetric,variable,BuildToolServiceImplemantation.getValueStream.metric,variable,transforms_to,,,assign,BuildToolServiceImplemantation,getValueStream
BuildToolServiceImplemantation.getValueStream.configSettingRep,variable,BuildToolServiceImplemantation.getValueStream.config,variable,transforms_to,,,assign,BuildToolServiceImplemantation,getValueStream
BuildToolServiceImplemantation.getGitlabValueStream.configSettingRepo,variable,BuildToolServiceImplemantation.getGitlabValueStream.config,variable,transforms_to,,,findByProjectName,BuildToolServiceImplemantation,getGitlabValueStream
BuildToolServiceImplemantation.getGitlabValueStream.ConfigurationToolInfoMetric,variable,BuildToolServiceImplemantation.getGitlabValueStream.metric,variable,transforms_to,,,assign,BuildToolServiceImplemantation,getGitlabValueStream
BuildToolServiceImplemantation.getGitlabValueStream.configSettingRep,variable,BuildToolServiceImplemantation.getGitlabValueStream.config,variable,transforms_to,,,assign,BuildToolServiceImplemantation,getGitlabValueStream
basemodel,file,basemodel,class,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BaseModel.java,UnifiedBolt,,,
basemodel,class,id,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BaseModel.java,UnifiedBolt,,,
basemodel,class,getid,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BaseModel.java,UnifiedBolt,,,
basemodel,class,setid,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BaseModel.java,UnifiedBolt,,,
BuildFailurePatternForProjectInJenkinsModel.addPatternMetric.BuildFailurePatternMetrics,variable,BuildFailurePatternForProjectInJenkinsModel.addPatternMetric.buildFailurePatternMetrics,variable,transforms_to,,,assign,BuildFailurePatternForProjectInJenkinsModel,addPatternMetric
buildfailurepatternforprojectinjenkinsmodel,class,basemodel,class,extends,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java,UnifiedBolt,,,
buildfailurepatternforprojectinjenkinsmodel,class,projectname,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java,UnifiedBolt,,,
buildfailurepatternforprojectinjenkinsmodel,class,username,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java,UnifiedBolt,,,
buildfailurepatternforprojectinjenkinsmodel,class,patternmetrics,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java,UnifiedBolt,,,
buildfailurepatternforprojectinjenkinsmodel,class,timestampofcreation,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java,UnifiedBolt,,,
buildfailurepatternforprojectinjenkinsmodel,class,getprojectname,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java,UnifiedBolt,,,
buildfailurepatternforprojectinjenkinsmodel,class,setprojectname,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java,UnifiedBolt,,,
buildfailurepatternforprojectinjenkinsmodel,class,getpatternmetrics,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java,UnifiedBolt,,,
buildfailurepatternforprojectinjenkinsmodel,class,setpatternmetrics,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java,UnifiedBolt,,,
buildfailurepatternforprojectinjenkinsmodel,class,gettimestampofcreation,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java,UnifiedBolt,,,
buildfailurepatternforprojectinjenkinsmodel,class,settimestampofcreation,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java,UnifiedBolt,,,
buildfailurepatternforprojectinjenkinsmodel,class,addpatternmetric,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java,UnifiedBolt,,,
buildfailurepatternforprojectinjenkinsmodel,class,getusername,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java,UnifiedBolt,,,
buildfailurepatternforprojectinjenkinsmodel,class,setusername,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java,UnifiedBolt,,,
buildfailurepatternmetrics,class,patterndefined,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildFailurePatternMetrics.java,UnifiedBolt,,,
buildfailurepatternmetrics,class,patterndisplayed,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildFailurePatternMetrics.java,UnifiedBolt,,,
buildfailurepatternmetrics,class,patterncount,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildFailurePatternMetrics.java,UnifiedBolt,,,
buildfailurepatternmetrics,class,reponame,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildFailurePatternMetrics.java,UnifiedBolt,,,
buildfailurepatternmetrics,class,getpatterndefined,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildFailurePatternMetrics.java,UnifiedBolt,,,
buildfailurepatternmetrics,class,setpatterndefined,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildFailurePatternMetrics.java,UnifiedBolt,,,
buildfailurepatternmetrics,class,getpatterndisplayed,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildFailurePatternMetrics.java,UnifiedBolt,,,
buildfailurepatternmetrics,class,setpatterndisplayed,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildFailurePatternMetrics.java,UnifiedBolt,,,
buildfailurepatternmetrics,class,getpatterncount,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildFailurePatternMetrics.java,UnifiedBolt,,,
buildfailurepatternmetrics,class,setpatterncount,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildFailurePatternMetrics.java,UnifiedBolt,,,
buildfailurepatternmetrics,class,getreponame,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildFailurePatternMetrics.java,UnifiedBolt,,,
buildfailurepatternmetrics,class,setreponame,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildFailurePatternMetrics.java,UnifiedBolt,,,
buildfileinfo,file,buildfileinfo,class,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildFileInfo.java,UnifiedBolt,,,
buildfileinfo,class,filenames,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildFileInfo.java,UnifiedBolt,,,
buildfileinfo,class,edittype,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildFileInfo.java,UnifiedBolt,,,
buildfileinfo,class,getfilenames,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildFileInfo.java,UnifiedBolt,,,
buildfileinfo,class,setfilenames,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildFileInfo.java,UnifiedBolt,,,
buildfileinfo,class,getedittype,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildFileInfo.java,UnifiedBolt,,,
buildfileinfo,class,setedittype,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildFileInfo.java,UnifiedBolt,,,
buildinfo,class,message,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildInfo.java,UnifiedBolt,,,
buildinfo,class,committer,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildInfo.java,UnifiedBolt,,,
buildinfo,class,buildfileinfolist,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildInfo.java,UnifiedBolt,,,
buildinfo,class,getmessage,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildInfo.java,UnifiedBolt,,,
buildinfo,class,setmessage,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildInfo.java,UnifiedBolt,,,
buildinfo,class,getcommitter,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildInfo.java,UnifiedBolt,,,
buildinfo,class,setcommitter,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildInfo.java,UnifiedBolt,,,
buildinfo,class,getbuildfileinfolist,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildInfo.java,UnifiedBolt,,,
buildinfo,class,setbuildfileinfolist,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildInfo.java,UnifiedBolt,,,
buildsteps,class,stepname,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildSteps.java,UnifiedBolt,,,
buildsteps,class,duration,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildSteps.java,UnifiedBolt,,,
buildsteps,class,result,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildSteps.java,UnifiedBolt,,,
buildsteps,class,startedtime,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildSteps.java,UnifiedBolt,,,
buildsteps,class,completedtime,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildSteps.java,UnifiedBolt,,,
buildsteps,class,getstepname,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildSteps.java,UnifiedBolt,,,
buildsteps,class,setstepname,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildSteps.java,UnifiedBolt,,,
buildsteps,class,getduration,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildSteps.java,UnifiedBolt,,,
buildsteps,class,setduration,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildSteps.java,UnifiedBolt,,,
buildsteps,class,getresult,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildSteps.java,UnifiedBolt,,,
buildsteps,class,setresult,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildSteps.java,UnifiedBolt,,,
buildsteps,class,getstartedtime,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildSteps.java,UnifiedBolt,,,
buildsteps,class,setstartedtime,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildSteps.java,UnifiedBolt,,,
buildsteps,class,getcompletedtime,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildSteps.java,UnifiedBolt,,,
buildsteps,class,setcompletedtime,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildSteps.java,UnifiedBolt,,,
buildtool,class,basemodel,class,extends,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,UnifiedBolt,,,
BuildTool.setJenkinsItemId.jenkinsItemId,variable,BuildTool.setJenkinsItemId.collectorItemId,variable,transforms_to,,,assign,BuildTool,setJenkinsItemId
buildtool,class,comparable,interface,implements,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,UnifiedBolt,,,
buildtool,class,collectoritemid,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,UnifiedBolt,,,
buildtool,class,timestamp,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,UnifiedBolt,,,
buildtool,class,timestring,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,UnifiedBolt,,,
buildtool,class,jobname,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,UnifiedBolt,,,
buildtool,class,url,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,UnifiedBolt,,,
buildtool,class,version,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,UnifiedBolt,,,
buildtool,class,buildtype,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,UnifiedBolt,,,
buildtool,class,buildid,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,UnifiedBolt,,,
buildtool,class,buildinfolist,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,UnifiedBolt,,,
buildtool,class,metrics,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,UnifiedBolt,,,
buildtool,class,stepslist,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,UnifiedBolt,,,
buildtool,class,joblist,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,UnifiedBolt,,,
buildtool,class,jobcount,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,UnifiedBolt,,,
buildtool,class,createdby,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,UnifiedBolt,,,
buildtool,class,branchname,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,UnifiedBolt,,,
buildtool,class,reponame,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,UnifiedBolt,,,
buildtool,class,groupname,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,UnifiedBolt,,,
buildtool,class,triggertype,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,UnifiedBolt,,,
buildtool,class,definitionid,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,UnifiedBolt,,,
buildtool,class,gettimestring,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,UnifiedBolt,,,
buildtool,class,settimestring,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,UnifiedBolt,,,
buildtool,class,getcollectoritemid,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,UnifiedBolt,,,
buildtool,class,setcollectoritemid,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,UnifiedBolt,,,
buildtool,class,getstepslist,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,UnifiedBolt,,,
buildtool,class,setstepslist,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,UnifiedBolt,,,
buildtool,class,getcreatedby,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,UnifiedBolt,,,
buildtool,class,setcreatedby,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,UnifiedBolt,,,
buildtool,class,getbranchname,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,UnifiedBolt,,,
buildtool,class,setbranchname,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,UnifiedBolt,,,
buildtool,class,setmetrics,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,UnifiedBolt,,,
buildtool,class,patterndetails,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,UnifiedBolt,,,
buildtool,class,getbuildid,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,UnifiedBolt,,,
buildtool,class,setbuildid,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,UnifiedBolt,,,
buildtool,class,getjenkinsitemid,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,UnifiedBolt,,,
buildtool,class,setjenkinsitemid,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,UnifiedBolt,,,
buildtool,class,gettimestamp,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,UnifiedBolt,,,
buildtool,class,settimestamp,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,UnifiedBolt,,,
buildtool,class,getname,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,UnifiedBolt,,,
buildtool,class,setname,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,UnifiedBolt,,,
buildtool,class,geturl,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,UnifiedBolt,,,
buildtool,class,seturl,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,UnifiedBolt,,,
buildtool,class,getversion,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,UnifiedBolt,,,
buildtool,class,setversion,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,UnifiedBolt,,,
buildtool,class,getmetrics,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,UnifiedBolt,,,
buildtool,class,getbuildtype,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,UnifiedBolt,,,
buildtool,class,setbuildtype,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,UnifiedBolt,,,
buildtool,class,getbuildinfolist,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,UnifiedBolt,,,
buildtool,class,setbuildinfolist,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,UnifiedBolt,,,
buildtool,class,getjobname,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,UnifiedBolt,,,
buildtool,class,setjobname,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,UnifiedBolt,,,
buildtool,class,getjoblist,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,UnifiedBolt,,,
buildtool,class,setjoblist,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,UnifiedBolt,,,
buildtool,class,getjobcount,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,UnifiedBolt,,,
buildtool,class,setjobcount,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,UnifiedBolt,,,
buildtool,class,getpatterndetails,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,UnifiedBolt,,,
buildtool,class,setpatterndetails,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,UnifiedBolt,,,
buildtool,class,compareto,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,UnifiedBolt,,,
buildtool,class,gettriggertype,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,UnifiedBolt,,,
buildtool,class,settriggertype,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,UnifiedBolt,,,
buildtool,class,getreponame,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,UnifiedBolt,,,
buildtool,class,setreponame,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,UnifiedBolt,,,
buildtool,class,getgroupname,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,UnifiedBolt,,,
buildtool,class,setgroupname,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,UnifiedBolt,,,
buildtool,class,getdefinitionid,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,UnifiedBolt,,,
buildtool,class,setdefinitionid,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,UnifiedBolt,,,
BuildToolMetric.setFormattedValue.formattedValue,variable,BuildToolMetric.setFormattedValue.formattedValueBuildTool,variable,transforms_to,,,assign,BuildToolMetric,setFormattedValue
BuildToolMetric.setStatusMessage.statusMessage,variable,BuildToolMetric.setStatusMessage.statusMessageBuildTool,variable,transforms_to,,,assign,BuildToolMetric,setStatusMessage
buildtoolmetric,class,valuebuildtool,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildToolMetric.java,UnifiedBolt,,,
buildtoolmetric,class,formattedvaluebuildtool,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildToolMetric.java,UnifiedBolt,,,
buildtoolmetric,class,statusmessagebuildtool,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildToolMetric.java,UnifiedBolt,,,
buildtoolmetric,class,getname,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildToolMetric.java,UnifiedBolt,,,
buildtoolmetric,class,setname,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildToolMetric.java,UnifiedBolt,,,
buildtoolmetric,class,getvalue,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildToolMetric.java,UnifiedBolt,,,
buildtoolmetric,class,setvalue,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildToolMetric.java,UnifiedBolt,,,
buildtoolmetric,class,getformattedvalue,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildToolMetric.java,UnifiedBolt,,,
buildtoolmetric,class,setformattedvalue,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildToolMetric.java,UnifiedBolt,,,
buildtoolmetric,class,getstatusmessage,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildToolMetric.java,UnifiedBolt,,,
buildtoolmetric,class,setstatusmessage,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildToolMetric.java,UnifiedBolt,,,
buildtoolmetric,class,equals,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildToolMetric.java,UnifiedBolt,,,
buildtoolmetric,class,hashcode,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildToolMetric.java,UnifiedBolt,,,
configurationsetting,file,configurationsetting,class,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationSetting.java,UnifiedBolt,,,
configurationsetting,class,basemodel,class,extends,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationSetting.java,UnifiedBolt,,,
configurationsetting,class,timestamp,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationSetting.java,UnifiedBolt,,,
configurationsetting,class,baseline,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationSetting.java,UnifiedBolt,,,
configurationsetting,class,projectname,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationSetting.java,UnifiedBolt,,,
configurationsetting,class,addflag,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationSetting.java,UnifiedBolt,,,
configurationsetting,class,projecttype,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationSetting.java,UnifiedBolt,,,
configurationsetting,class,manualdata,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationSetting.java,UnifiedBolt,,,
configurationsetting,class,ismanualdata,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationSetting.java,UnifiedBolt,,,
configurationsetting,class,setmanualdata,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationSetting.java,UnifiedBolt,,,
configurationsetting,class,gettimestamp,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationSetting.java,UnifiedBolt,,,
configurationsetting,class,settimestamp,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationSetting.java,UnifiedBolt,,,
configurationsetting,class,getprojectname,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationSetting.java,UnifiedBolt,,,
configurationsetting,class,setprojectname,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationSetting.java,UnifiedBolt,,,
configurationsetting,class,getmetrics,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationSetting.java,UnifiedBolt,,,
configurationsetting,class,isaddflag,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationSetting.java,UnifiedBolt,,,
configurationsetting,class,setaddflag,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationSetting.java,UnifiedBolt,,,
configurationsetting,class,isbaseline,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationSetting.java,UnifiedBolt,,,
configurationsetting,class,setbaseline,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationSetting.java,UnifiedBolt,,,
configurationsetting,class,getprojecttype,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationSetting.java,UnifiedBolt,,,
configurationsetting,class,setprojecttype,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationSetting.java,UnifiedBolt,,,
configurationtoolinfometric,class,selected,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt,,,
configurationtoolinfometric,class,id,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt,,,
configurationtoolinfometric,class,toolname,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt,,,
configurationtoolinfometric,class,url,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt,,,
configurationtoolinfometric,class,username,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt,,,
configurationtoolinfometric,class,password,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt,,,
configurationtoolinfometric,class,tooltype,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt,,,
configurationtoolinfometric,class,widgetname,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt,,,
configurationtoolinfometric,class,jobname,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt,,,
configurationtoolinfometric,class,projectcode,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt,,,
configurationtoolinfometric,class,domain,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt,,,
configurationtoolinfometric,class,dbtype,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt,,,
configurationtoolinfometric,class,schema,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt,,,
configurationtoolinfometric,class,reponame,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt,,,
configurationtoolinfometric,class,secret,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt,,,
configurationtoolinfometric,class,getid,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt,,,
configurationtoolinfometric,class,setid,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt,,,
configurationtoolinfometric,class,gettoolname,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt,,,
configurationtoolinfometric,class,settoolname,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt,,,
configurationtoolinfometric,class,geturl,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt,,,
configurationtoolinfometric,class,seturl,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt,,,
configurationtoolinfometric,class,getusername,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt,,,
configurationtoolinfometric,class,setusername,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt,,,
configurationtoolinfometric,class,getpassword,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt,,,
configurationtoolinfometric,class,setpassword,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt,,,
configurationtoolinfometric,class,gettooltype,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt,,,
configurationtoolinfometric,class,settooltype,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt,,,
configurationtoolinfometric,class,getwidgetname,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt,,,
configurationtoolinfometric,class,setwidgetname,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt,,,
configurationtoolinfometric,class,getjobname,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt,,,
configurationtoolinfometric,class,setjobname,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt,,,
configurationtoolinfometric,class,getprojectcode,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt,,,
configurationtoolinfometric,class,setprojectcode,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt,,,
configurationtoolinfometric,class,getselected,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt,,,
configurationtoolinfometric,class,setselected,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt,,,
configurationtoolinfometric,class,getdomain,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt,,,
configurationtoolinfometric,class,setdomain,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt,,,
configurationtoolinfometric,class,getdbtype,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt,,,
configurationtoolinfometric,class,setdbtype,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt,,,
configurationtoolinfometric,class,getschema,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt,,,
configurationtoolinfometric,class,setschema,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt,,,
configurationtoolinfometric,class,getreponame,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt,,,
configurationtoolinfometric,class,setreponame,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt,,,
configurationtoolinfometric,class,getsecret,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt,,,
configurationtoolinfometric,class,setsecret,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt,,,
githubactionapplication,file,githubactionapplication,class,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt,,,
githubactionapplication,class,logger,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt,,,
githubactionapplication,class,applicationcontext,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt,,,
githubactionapplication,class,githubactionmetrics,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt,,,
githubactionapplication,class,result,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt,,,
githubactionapplication,class,buildtype,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt,,,
githubactionapplication,class,pagelimit,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt,,,
githubactionapplication,class,configurationrepo,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt,,,
githubactionapplication,class,configuration,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt,,,
githubactionapplication,class,metric,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt,,,
githubactionapplication,class,metric1,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt,,,
githubactionapplication,class,githubactionmain,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt,,,
githubactionapplication,class,cleanobject,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt,,,
githubactionapplication,class,parseasarray,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt,,,
githubactionapplication,class,makerestcall,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt,,,
logmanager,variable,logger,variable,transforms_to,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt,,,
getlogger,variable,logger,variable,transforms_to,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt,,,
dataconfig,variable,applicationcontext,variable,transforms_to,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt,,,
getcontext,variable,applicationcontext,variable,transforms_to,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt,,,
applicationcontext,variable,configurationrepo,variable,transforms_to,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt,,,
configurationrepo,variable,configuration,variable,transforms_to,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt,,,
findbyprojectname,variable,configuration,variable,transforms_to,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt,,,
configuration,variable,metric,variable,transforms_to,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt,,,
metric1,variable,instanceurl,variable,transforms_to,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt,,,
geturl,variable,instanceurl,variable,transforms_to,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt,,,
metric1,variable,username,variable,transforms_to,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt,,,
getusername,variable,username,variable,transforms_to,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt,,,
encryptiondecryptionaes,variable,password,variable,transforms_to,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt,,,
decrypt,variable,password,variable,transforms_to,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt,,,
metric1,variable,reponame,variable,transforms_to,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt,,,
getreponame,variable,reponame,variable,transforms_to,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt,,,
instanceurl,variable,spliturl,variable,transforms_to,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt,,,
split,variable,spliturl,variable,transforms_to,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt,,,
reponame,variable,splitreponame,variable,transforms_to,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt,,,
split,variable,splitreponame,variable,transforms_to,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt,,,
GithubActionApplication.makeRestCall.HttpComponentsClientHttpRequestFactory,variable,GithubActionApplication.makeRestCall.requestFactory,variable,transforms_to,,,assign,GithubActionApplication,makeRestCall
githubactionimplementation,file,githubactionimplementation,class,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt,,,
githubactionimplementation,class,githubaction,interface,implements,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt,,,
githubactionimplementation,class,logger,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt,,,
githubactionimplementation,class,segment_api,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt,,,
githubactionimplementation,class,public_github_repo_host,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt,,,
githubactionimplementation,class,public_github_host_name,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt,,,
githubactionimplementation,class,ctx,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt,,,
githubactionimplementation,class,projectname,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt,,,
githubactionimplementation,class,username,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt,,,
githubactionimplementation,class,password,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt,,,
githubactionimplementation,class,buildrepo,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt,,,
githubactionimplementation,class,jobcollection,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt,,,
githubactionimplementation,class,pipelineurl,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt,,,
githubactionimplementation,class,singlepipelineurl,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt,,,
githubactionimplementation,class,githuburl,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt,,,
githubactionimplementation,class,jobsurl,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt,,,
githubactionimplementation,class,lastpage,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt,,,
githubactionimplementation,class,lastbuildid,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt,,,
githubactionimplementation,class,newbuildpipelinetimestamp,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt,,,
githubactionimplementation,class,timestamp,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt,,,
githubactionimplementation,class,per_page,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt,,,
githubactionimplementation,class,totalpages,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt,,,
githubactionimplementation,class,pipelineid,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt,,,
githubactionimplementation,class,build,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt,,,
githubactionimplementation,class,jobslist,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt,,,
githubactionimplementation,class,failurepatternrepo,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt,,,
githubactionimplementation,class,reponame,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt,,,
githubactionimplementation,class,getbuildtooldata,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt,,,
githubactionimplementation,class,processpipelinedata,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt,,,
githubactionimplementation,class,processfailure,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt,,,
githubactionimplementation,class,gettimeinmiliseconds,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt,,,
githubactionimplementation,class,makerestcall,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt,,,
githubactionimplementation,class,createheaders,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt,,,
GithubActionImplementation.getBuildToolData.baseUrl,variable,GithubActionImplementation.getBuildToolData.githubUrl,variable,transforms_to,,,assign,GithubActionImplementation,getBuildToolData
GithubActionImplementation.while.makeRestCall,variable,GithubActionImplementation.while.response,variable,transforms_to,,,assign,GithubActionImplementation,while
GithubActionImplementation.while.githubUrl,variable,GithubActionImplementation.while.pipelineUrl,variable,transforms_to,,,assign,GithubActionImplementation,while
GithubActionImplementation.while.makeRestCal,variable,GithubActionImplementation.while.response,variable,transforms_to,,,assign,GithubActionImplementation,while
GithubActionImplementation.while.processPipelineDat,variable,GithubActionImplementation.while.builds,variable,transforms_to,,,assign,GithubActionImplementation,while
githubactionimplementation,class,get,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt,,,
GithubActionImplementation.processPipelineData.getTimeInMiliseconds,variable,GithubActionImplementation.processPipelineData.timestamp,variable,transforms_to,,,assign,GithubActionImplementation,processPipelineData
GithubActionImplementation.processPipelineData.makeRestCall,variable,GithubActionImplementation.processPipelineData.jobResponse,variable,transforms_to,,,assign,GithubActionImplementation,processPipelineData
GithubActionImplementation.processPipelineData.pipelineValues,variable,GithubActionImplementation.processPipelineData.pipeline_Obj,variable,transforms_to,,,getJSONObject,GithubActionImplementation,processPipelineData
GithubActionImplementation.processPipelineData.BuildTool,variable,GithubActionImplementation.processPipelineData.build,variable,transforms_to,,,assign,GithubActionImplementation,processPipelineData
GithubActionImplementation.processPipelineData.pipelineValue,variable,GithubActionImplementation.processPipelineData.pipeline_Obj,variable,transforms_to,,,assign,GithubActionImplementation,processPipelineData
GithubActionImplementation.processPipelineData.getTimeInMilisecond,variable,GithubActionImplementation.processPipelineData.timestamp,variable,transforms_to,,,assign,GithubActionImplementation,processPipelineData
GithubActionImplementation.processPipelineData.githubUrl,variable,GithubActionImplementation.processPipelineData.jobsUrl,variable,transforms_to,,,assign,GithubActionImplementation,processPipelineData
GithubActionImplementation.processPipelineData.makeRestCal,variable,GithubActionImplementation.processPipelineData.jobResponse,variable,transforms_to,,,assign,GithubActionImplementation,processPipelineData
updatedtime,variable,duration,variable,transforms_to,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt,,,
createtime,variable,duration,variable,transforms_to,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt,,,
substring,variable,githuburl,variable,transforms_to,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt,,,
pipelinevalues,variable,pipeline_obj,variable,transforms_to,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt,,,
getjsonobject,variable,pipeline_obj,variable,transforms_to,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt,,,
pipeline_obj,variable,temp_date,variable,transforms_to,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt,,,
optstring,variable,temp_date,variable,transforms_to,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt,,,
pipeline_obj,variable,temp_date1,variable,transforms_to,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt,,,
optstring,variable,temp_date1,variable,transforms_to,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt,,,
getstring,variable,temp_date,variable,transforms_to,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt,,,
jobsarray1,variable,jobsarray,variable,transforms_to,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt,,,
getjsonarray,variable,jobsarray,variable,transforms_to,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt,,,
jobsarray,variable,singlejobsobj,variable,transforms_to,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt,,,
getjsonobject,variable,singlejobsobj,variable,transforms_to,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt,,,
pipeline_obj,variable,commitobj,variable,transforms_to,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt,,,
getjsonobject,variable,commitobj,variable,transforms_to,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt,,,
commitobj,variable,authorobj,variable,transforms_to,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt,,,
getjsonobject,variable,authorobj,variable,transforms_to,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt,,,
jobsarray,variable,stepsobj,variable,transforms_to,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt,,,
getjsonobject,variable,stepsobj,variable,transforms_to,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt,,,
stepsobj,variable,completedate,variable,transforms_to,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt,,,
getstring,variable,completedate,variable,transforms_to,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt,,,
stepsobj,variable,result,variable,transforms_to,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt,,,
optstring,variable,result,variable,transforms_to,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt,,,
failurepatternrepo,variable,failurepattern,variable,transforms_to,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt,,,
findbyprojectname,variable,failurepattern,variable,transforms_to,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt,,,
tempbuildfailure,variable,failuremetrics,variable,transforms_to,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt,,,
getpatternmetrics,variable,failuremetrics,variable,transforms_to,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt,,,
logresponsedata,variable,failurelog,variable,transforms_to,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt,,,
getbody,variable,failurelog,variable,transforms_to,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt,,,
temp_date,variable,splitdate,variable,transforms_to,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt,,,
split,variable,splitdate,variable,transforms_to,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt,,,
base64,variable,encodedauth,variable,transforms_to,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt,,,
encodebase64,variable,encodedauth,variable,transforms_to,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt,,,
GithubActionImplementation.processFailure.makeRestCall,variable,GithubActionImplementation.processFailure.logResponseData,variable,transforms_to,,,assign,GithubActionImplementation,processFailure
GithubActionImplementation.processFailure.failurePatternRepo,variable,GithubActionImplementation.processFailure.failurePattern,variable,transforms_to,,,findByProjectName,GithubActionImplementation,processFailure
GithubActionImplementation.processFailure.failurePattern,variable,GithubActionImplementation.processFailure.tempBuildFailure,variable,transforms_to,,,get,GithubActionImplementation,processFailure
GithubActionImplementation.processFailure.tempBuildFailure,variable,GithubActionImplementation.processFailure.failureMetrics,variable,transforms_to,,,getPatternMetrics,GithubActionImplementation,processFailure
GithubActionImplementation.processFailure.logResponseData,variable,GithubActionImplementation.processFailure.failureLog,variable,transforms_to,,,getBody,GithubActionImplementation,processFailure
GithubActionImplementation.processFailure.failurePatternRep,variable,GithubActionImplementation.processFailure.failurePattern,variable,transforms_to,,,assign,GithubActionImplementation,processFailure
GithubActionImplementation.processFailure.failurePatter,variable,GithubActionImplementation.processFailure.tempBuildFailure,variable,transforms_to,,,assign,GithubActionImplementation,processFailure
GithubActionImplementation.processFailure.tempBuildFailur,variable,GithubActionImplementation.processFailure.failureMetrics,variable,transforms_to,,,assign,GithubActionImplementation,processFailure
GithubActionImplementation.processFailure.makeRestCal,variable,GithubActionImplementation.processFailure.logResponseData,variable,transforms_to,,,assign,GithubActionImplementation,processFailure
GithubActionImplementation.processFailure.logResponseDat,variable,GithubActionImplementation.processFailure.failureLog,variable,transforms_to,,,assign,GithubActionImplementation,processFailure
GithubActionImplementation.createHeaders.Base64,variable,GithubActionImplementation.createHeaders.encodedAuth,variable,transforms_to,,,encodeBase64,GithubActionImplementation,createHeaders
GithubActionImplementation.createHeaders.HttpHeaders,variable,GithubActionImplementation.createHeaders.headers,variable,transforms_to,,,assign,GithubActionImplementation,createHeaders
GithubActionImplementation.createHeaders.Base6,variable,GithubActionImplementation.createHeaders.encodedAuth,variable,transforms_to,,,assign,GithubActionImplementation,createHeaders
