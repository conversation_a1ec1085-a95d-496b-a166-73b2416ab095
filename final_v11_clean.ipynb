# ========== STAGE 1: SETUP AND CONFIGURATION ==========

import os
import pandas as pd
import re
from pathlib import Path
from tqdm import tqdm
from collections import defaultdict
import warnings
warnings.filterwarnings('ignore')

# Tree-sitter for AST parsing
from tree_sitter import Language, Parser
import tree_sitter_java as tsjava

# LangChain for LLM processing
from langchain_community.document_loaders import TextLoader
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain_google_genai import ChatGoogleGenerativeAI
from langchain_experimental.graph_transformers import LLMGraphTransformer
from langchain_community.graphs import Neo4jGraph

# ========== CONFIGURATION ==========
BASE_PATH = Path(r'C:/Shaik/sample/OneInsights')
NEO4J_URI = 'bolt://localhost:7687'
NEO4J_USER = 'neo4j'
NEO4J_PASSWORD = 'Test@7889'
NEO4J_DB = 'oneinsights-v11'
GOOGLE_API_KEY = 'AIzaSyD8qIjUPsRbhJr59qoIIhczbPPKA2hter0'

# ========== INITIALIZE COMPONENTS ==========
print('🔧 Initializing components...')

# Neo4j connection
try:
    graph = Neo4jGraph(url=NEO4J_URI, username=NEO4J_USER, password=NEO4J_PASSWORD, database=NEO4J_DB)
    print('✅ Neo4j connection established')
except Exception as e:
    print(f'❌ Neo4j connection failed: {e}')
    graph = None

# Tree-sitter Java parser
JAVA_LANGUAGE = Language(tsjava.language())
parser = Parser(JAVA_LANGUAGE)

# LLM for enhanced extraction
llm = ChatGoogleGenerativeAI(
    model='gemini-2.0-flash-exp',
    temperature=0,
    google_api_key=GOOGLE_API_KEY
)

# ========== APPLICATION MAPPING ==========
APPLICATIONS = {
    'ServiceBolt': 'REST API Service Layer',
    'UnifiedBolt': 'Core Business Logic and Data Layer'
}

# ========== NOISE FILTERING ==========
NOISE_VARIABLES = {
    'i', 'j', 'k', 'l', 'm', 'n', 'x', 'y', 'z',
    'temp', 'tmp', 'obj', 'item', 'elem', 'node', 'val', 'value',
    'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w',
    'this', 'super', 'null', 'true', 'false', 'void', 'return',
    'it', 'ex', 'e1', 'e2', 'o1', 'o2'
}

def is_meaningful_variable(var_name):
    '''Filter meaningful variables only'''
    if not var_name or len(var_name) < 3:
        return False
    if var_name.lower() in NOISE_VARIABLES:
        return False
    if re.match(r'^[a-zA-Z][a-zA-Z0-9_]*$', var_name):
        return True
    return False

# ========== GLOBAL DATA STRUCTURES ==========
all_relationships = []  # Master list of all relationships
class_registry = {}     # Track all classes and their details
method_registry = {}    # Track all methods and their details
variable_registry = {}  # Track all variables and their context

print('🚀 Setup complete! Ready for clean v11 analysis...')

# ========== STAGE 2: FILE DISCOVERY AND HIERARCHY BUILDING ==========

def build_project_hierarchy():
    '''Build complete project hierarchy: PROJECT → APPLICATION → FOLDER → FILE'''
    print('🏗️ Building project hierarchy...')
    
    hierarchy_relationships = []
    
    # PROJECT level
    project_name = 'OneInsights'
    
    # APPLICATION level - Connect project to BOTH applications
    for app_name in APPLICATIONS.keys():
        hierarchy_relationships.append({
            'source_node': project_name,
            'source_type': 'project',
            'destination_node': app_name,
            'destination_type': 'application',
            'relationship': 'contains',
            'file_path': None,
            'application': app_name
        })
    
    # FOLDER and FILE level
    for app_name in APPLICATIONS.keys():
        app_path = BASE_PATH / app_name
        if not app_path.exists():
            print(f'⚠️ Application path not found: {app_path}')
            continue
        
        print(f'📁 Processing application: {app_name}')
        
        # Find all Java files
        java_files = list(app_path.rglob('*.java'))
        print(f'   Found {len(java_files)} Java files')
        
        folders_processed = set()
        
        for java_file in java_files:
            relative_path = java_file.relative_to(app_path)
            path_parts = relative_path.parts
            
            # Build folder hierarchy
            current_parent = app_name
            current_parent_type = 'application'
            
            # Process each folder in the path
            for i, folder in enumerate(path_parts[:-1]):  # Exclude the file itself
                folder_key = '/'.join(path_parts[:i+1])
                
                if folder_key not in folders_processed:
                    # APPLICATION/FOLDER → FOLDER relationship
                    hierarchy_relationships.append({
                        'source_node': current_parent,
                        'source_type': current_parent_type,
                        'destination_node': folder,
                        'destination_type': 'folder',
                        'relationship': 'contains',
                        'file_path': str(java_file.parent),
                        'application': app_name
                    })
                    folders_processed.add(folder_key)
                
                current_parent = folder
                current_parent_type = 'folder'
            
            # FOLDER → FILE relationship
            file_name = java_file.stem  # Without .java extension
            hierarchy_relationships.append({
                'source_node': current_parent,
                'source_type': current_parent_type,
                'destination_node': file_name,
                'destination_type': 'file',
                'relationship': 'contains',
                'file_path': str(java_file),
                'application': app_name
            })
    
    print(f'✅ Built hierarchy with {len(hierarchy_relationships)} relationships')
    return hierarchy_relationships

# Build the hierarchy
hierarchy_rels = build_project_hierarchy()
all_relationships.extend(hierarchy_rels)

print(f'📊 Current total relationships: {len(all_relationships)}')

# ========== STAGE 3: AST-BASED CODE ANALYSIS ==========

def extract_class_info(node, source_code):
    '''Extract class information from AST node'''
    class_name = None
    extends_class = None
    
    for child in node.children:
        if child.type == 'identifier':
            class_name = source_code[child.start_byte:child.end_byte]
        elif child.type == 'superclass':
            for subchild in child.children:
                if subchild.type == 'type_identifier':
                    extends_class = source_code[subchild.start_byte:subchild.end_byte]
    
    return class_name, extends_class

def extract_method_info(node, source_code):
    '''Extract method information from AST node'''
    method_name = None
    parameters = []
    
    for child in node.children:
        if child.type == 'identifier':
            method_name = source_code[child.start_byte:child.end_byte]
        elif child.type == 'formal_parameters':
            for param_child in child.children:
                if param_child.type == 'formal_parameter':
                    for param_part in param_child.children:
                        if param_part.type == 'variable_declarator':
                            for var_part in param_part.children:
                                if var_part.type == 'identifier':
                                    param_name = source_code[var_part.start_byte:var_part.end_byte]
                                    if is_meaningful_variable(param_name):
                                        parameters.append(param_name)
    
    return method_name, parameters

def extract_variables_from_node(node, source_code, method_context=None):
    '''Extract variables from any AST node'''
    variables = []
    
    def traverse_node(n):
        if n.type == 'variable_declarator':
            for child in n.children:
                if child.type == 'identifier':
                    var_name = source_code[child.start_byte:child.end_byte]
                    if is_meaningful_variable(var_name):
                        variables.append(var_name)
        
        for child in n.children:
            traverse_node(child)
    
    traverse_node(node)
    return variables

def analyze_java_file_ast(file_path, app_name):
    '''Analyze Java file using AST and extract all relationships'''
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            source_code = f.read()
    except Exception as e:
        print(f'❌ Error reading {file_path}: {e}')
        return []
    
    tree = parser.parse(bytes(source_code, 'utf8'))
    relationships = []
    
    file_name = Path(file_path).stem
    current_class = None
    current_method = None
    
    def traverse_ast(node, depth=0):
        nonlocal current_class, current_method
        
        if node.type == 'class_declaration':
            class_name, extends_class = extract_class_info(node, source_code)
            if class_name:
                current_class = class_name
                
                # Register class
                class_registry[class_name] = {
                    'file_path': file_path,
                    'application': app_name,
                    'extends': extends_class
                }
                
                # FILE → CLASS relationship
                relationships.append({
                    'source_node': file_name,
                    'source_type': 'file',
                    'destination_node': class_name,
                    'destination_type': 'class',
                    'relationship': 'contains',
                    'file_path': file_path,
                    'application': app_name
                })
                
                # CLASS → EXTENDS relationship
                if extends_class:
                    relationships.append({
                        'source_node': class_name,
                        'source_type': 'class',
                        'destination_node': extends_class,
                        'destination_type': 'class',
                        'relationship': 'extends',
                        'file_path': file_path,
                        'application': app_name
                    })
        
        elif node.type == 'method_declaration' and current_class:
            method_name, parameters = extract_method_info(node, source_code)
            if method_name:
                current_method = method_name
                
                # Register method
                method_key = f"{current_class}.{method_name}"
                method_registry[method_key] = {
                    'class': current_class,
                    'file_path': file_path,
                    'application': app_name,
                    'parameters': parameters
                }
                
                # CLASS → METHOD relationship
                relationships.append({
                    'source_node': current_class,
                    'source_type': 'class',
                    'destination_node': method_name,
                    'destination_type': 'method',
                    'relationship': 'declares',
                    'file_path': file_path,
                    'application': app_name
                })
                
                # Extract variables from method
                method_variables = extract_variables_from_node(node, source_code, method_name)
                for var_name in method_variables:
                    # Create standardized variable name
                    standardized_var = f"{current_class}.{method_name}.{var_name}"
                    
                    # Register variable
                    variable_registry[standardized_var] = {
                        'original_name': var_name,
                        'class': current_class,
                        'method': method_name,
                        'file_path': file_path,
                        'application': app_name
                    }
                    
                    # METHOD → VARIABLE relationship
                    relationships.append({
                        'source_node': method_name,
                        'source_type': 'method',
                        'destination_node': standardized_var,
                        'destination_type': 'variable',
                        'relationship': 'uses',
                        'file_path': file_path,
                        'application': app_name
                    })
        
        # Continue traversing
        for child in node.children:
            traverse_ast(child, depth + 1)
    
    traverse_ast(tree.root_node)
    return relationships

# Process all Java files
print('🔍 Analyzing Java files with AST...')
ast_relationships = []

for app_name in APPLICATIONS.keys():
    app_path = BASE_PATH / app_name
    if app_path.exists():
        java_files = list(app_path.rglob('*.java'))
        print(f'📁 Processing {len(java_files)} files in {app_name}...')
        
        for java_file in tqdm(java_files, desc=f'AST Analysis - {app_name}'):
            file_rels = analyze_java_file_ast(java_file, app_name)
            ast_relationships.extend(file_rels)

all_relationships.extend(ast_relationships)
print(f'✅ AST analysis complete. Added {len(ast_relationships)} relationships')
print(f'📊 Total relationships: {len(all_relationships)}')
print(f'📊 Registered: {len(class_registry)} classes, {len(method_registry)} methods, {len(variable_registry)} variables')

# ========== STAGE 4: VARIABLE STANDARDIZATION AND CONNECTION BUILDING ==========

def create_variable_transformations():
    '''Create variable transformation relationships based on method flow'''
    print('🔗 Creating variable transformations...')
    
    transformation_relationships = []
    
    # Group variables by class and method
    method_variables = defaultdict(list)
    for var_name, var_info in variable_registry.items():
        method_key = f"{var_info['class']}.{var_info['method']}"
        method_variables[method_key].append(var_name)
    
    # Create transformations within methods
    for method_key, variables in method_variables.items():
        if len(variables) > 1:
            # Create transformation chain
            for i in range(len(variables) - 1):
                source_var = variables[i]
                target_var = variables[i + 1]
                
                var_info = variable_registry[source_var]
                
                transformation_relationships.append({
                    'source_node': source_var,
                    'source_type': 'variable',
                    'destination_node': target_var,
                    'destination_type': 'variable',
                    'relationship': 'transforms_to',
                    'file_path': var_info['file_path'],
                    'application': var_info['application']
                })
    
    print(f'✅ Created {len(transformation_relationships)} variable transformations')
    return transformation_relationships

def create_class_variable_connections():
    '''Create CLASS → VARIABLE relationships for all standardized variables'''
    print('🔗 Creating class-variable connections...')
    
    class_var_relationships = []
    
    for var_name, var_info in variable_registry.items():
        class_var_relationships.append({
            'source_node': var_info['class'],
            'source_type': 'class',
            'destination_node': var_name,
            'destination_type': 'variable',
            'relationship': 'declares_variable',
            'file_path': var_info['file_path'],
            'application': var_info['application']
        })
    
    print(f'✅ Created {len(class_var_relationships)} class-variable connections')
    return class_var_relationships

def create_cross_class_connections():
    '''Create connections between classes that use similar variables'''
    print('🔗 Creating cross-class connections...')
    
    cross_connections = []
    
    # Group variables by original name
    original_name_groups = defaultdict(list)
    for var_name, var_info in variable_registry.items():
        original_name_groups[var_info['original_name']].append((var_name, var_info))
    
    # Create connections between classes that use the same variable names
    for original_name, var_list in original_name_groups.items():
        if len(var_list) > 1:
            # Create data flow connections
            for i in range(len(var_list) - 1):
                source_var, source_info = var_list[i]
                target_var, target_info = var_list[i + 1]
                
                if source_info['class'] != target_info['class']:
                    cross_connections.append({
                        'source_node': source_var,
                        'source_type': 'variable',
                        'destination_node': target_var,
                        'destination_type': 'variable',
                        'relationship': 'data_find',
                        'file_path': source_info['file_path'],
                        'application': source_info['application']
                    })
    
    print(f'✅ Created {len(cross_connections)} cross-class connections')
    return cross_connections

# Create all variable-related relationships
var_transformations = create_variable_transformations()
class_var_connections = create_class_variable_connections()
cross_class_connections = create_cross_class_connections()

# Add to master list
all_relationships.extend(var_transformations)
all_relationships.extend(class_var_connections)
all_relationships.extend(cross_class_connections)

print(f'✅ Variable standardization complete')
print(f'📊 Total relationships: {len(all_relationships)}')

# ========== STAGE 4B: LLM PREPARATION WITH CURRENT CONTEXT ==========

def build_focused_system_prompt_v11(file_path, current_relationships, class_registry):
    '''Build system prompt with current AST context for LLM'''
    
    # Filter relationships for this specific file
    file_relationships = [r for r in current_relationships if r.get('file_path') == file_path]
    
    # Build AST context from current relationships
    ast_context = 'CURRENT AST RELATIONSHIPS:\n'
    for rel in file_relationships[:20]:  # Limit to avoid token overflow
        ast_context += f"{rel['source_type']}:{rel['source_node']} -[{rel['relationship']}]-> {rel['destination_type']}:{rel['destination_node']}\n"
    
    # Build class registry context
    registry_context = 'KNOWN CLASSES:\n'
    for class_name, info in class_registry.items():
        registry_context += f'- {class_name} (app: {info["application"]})\n'
    
    # Build method context
    method_context = 'KNOWN METHODS:\n'
    for method_key, info in method_registry.items():
        method_context += f'- {method_key} (class: {info["class"]})\n'
    
    prompt = f"""
You are a Java code lineage extraction engine. Extract relationships between code entities using CURRENT CONTEXT:

CURRENT CONTEXT:
{registry_context}

{method_context}

{ast_context}

CRITICAL RULES - FOCUSED EXTRACTION:
1. Use SIMPLE names only (remove prefixes like "method:", "class:", etc.)
2. MANDATORY RELATIONSHIP DIRECTIONS:
   - class -[declares]-> method  
   - class -[declares_variable]-> variable
   - class -[exposes]-> endpoint
   - class -[extends]-> class (prioritized)
   - class -[data_find]-> data
   - method -[uses]-> variable
   - variable -[transforms_to]-> variable (meaningful only)
3. Extract REST API endpoints as 'endpoint' nodes (GET /api/users, POST /api/data)
4. Extract database tables/entities as 'data' nodes
5. Focus on MEANINGFUL variables only (length > 3 or business-relevant)
6. Prioritize EXTENDS over IMPLEMENTS relationships
7. Use standardized variable format: ClassName.methodName.variableName
8. Clean node names (remove "method:", "class:" prefixes)

Extract triples in format:
[SourceType]:SourceName -[RELATIONSHIP]-> [TargetType]:TargetName

Return ONLY the triples, no explanations.
"""
    return prompt

def extract_enhanced_variable_lineage_v11(source_code_str, class_name, method_context=None):
    '''Extract variable lineage with standardized naming'''
    variable_lineage = []
    
    # Enhanced variable assignment patterns
    assignment_patterns = [
        # Method return assignments: result = methodName(...)
        r'(\w{3,})\s*=\s*(\w{3,})\s*\(.*?\)',
        # Service/Repository calls: result = serviceInstance.methodName(...)
        r'(\w{3,})\s*=\s*(\w{3,})\s*\.\s*(\w+)\s*\(',
        # Object creation: result = new ClassName(...)
        r'(\w{3,})\s*=\s*new\s+(\w+)\s*\(',
        # Field access: result = object.fieldName
        r'(\w{3,})\s*=\s*(\w{3,})\s*\.\s*(\w+)(?!\s*\()',
        # Simple assignments: result = variable
        r'(\w{3,})\s*=\s*(\w{3,})(?!\s*[\(\.])',
    ]
    
    for pattern in assignment_patterns:
        matches = re.findall(pattern, source_code_str, re.MULTILINE)
        for match in matches:
            if len(match) >= 2:
                result_var = match[0]
                source_identifier = match[1]
                
                if (is_meaningful_variable(result_var) and 
                    is_meaningful_variable(source_identifier) and
                    result_var != source_identifier):
                    
                    # Create standardized variable names
                    method_name = method_context or 'field'
                    result_standardized = f"{class_name}.{method_name}.{result_var}"
                    source_standardized = f"{class_name}.{method_name}.{source_identifier}"
                    
                    variable_lineage.append({
                        'source_node': source_standardized,
                        'source_type': 'variable',
                        'destination_node': result_standardized,
                        'destination_type': 'variable',
                        'relationship': 'transforms_to',
                        'file_path': None,  # Will be set by caller
                        'application': None  # Will be set by caller
                    })
    
    return variable_lineage

# Prepare documents for LLM processing
print('📄 Preparing documents for LLM processing...')

splitter = RecursiveCharacterTextSplitter.from_language(
    language=LC_Language.JAVA,
    chunk_size=4000,
    chunk_overlap=200
)

java_docs, split_docs = [], []

for app_name in APPLICATIONS.keys():
    app_path = BASE_PATH / app_name
    if app_path.exists():
        for java_file in app_path.rglob('*.java'):
            try:
                loader = TextLoader(str(java_file), encoding='utf-8')
                docs = loader.load()
                java_docs.extend(docs)
            except Exception as e:
                continue

for doc in java_docs:
    split_docs.extend(splitter.split_documents([doc]))

print(f'📄 Prepared {len(split_docs)} document chunks for LLM processing')

# ========== STAGE 4C: LLM PROCESSING WITH CURRENT CONTEXT ==========

def normalize_entity_v11(entity_name, entity_type):
    '''Normalize entity names for consistency'''
    if not entity_name:
        return entity_name
    
    # Remove prefixes
    prefixes = ['method:', 'class:', 'variable:', 'field:', 'table:', 'endpoint:']
    for prefix in prefixes:
        if entity_name.lower().startswith(prefix):
            entity_name = entity_name[len(prefix):]
    
    # Remove file extensions
    entity_name = re.sub(r'\.(java|class)$', '', entity_name, flags=re.IGNORECASE)
    
    # Clean dots for class names (but preserve for standardized variables)
    if entity_type in ['class', 'method'] and '.' in entity_name and not entity_type == 'variable':
        entity_name = entity_name.split('.')[-1]
    
    # Match with class registry for consistency
    if entity_type == 'class':
        for class_name in class_registry.keys():
            if entity_name.lower() == class_name.lower():
                return class_name
    
    return entity_name

def extract_method_variable_context_v11(source_code, class_name):
    '''Extract variables with method context using current approach'''
    method_variables = []
    
    # Find method declarations
    method_pattern = r'(?:public|private|protected)?\s*(?:static)?\s*(?:\w+\s+)*(\w+)\s*\([^)]*\)\s*\{([^}]*(?:\{[^}]*\}[^}]*)*)\}'
    method_matches = re.findall(method_pattern, source_code, re.DOTALL)
    
    for method_name, method_body in method_matches:
        if is_meaningful_variable(method_name):
            # Extract variable lineage within this method
            method_var_lineage = extract_enhanced_variable_lineage_v11(method_body, class_name, method_name)
            method_variables.extend(method_var_lineage)
    
    return method_variables

# Initialize LLM lineage collection
all_llm_lineage = []

print('🤖 Starting LLM extraction with current context...')

for chunk in tqdm(split_docs, desc='LLM Processing'):
    file_path = chunk.metadata.get('source')
    
    # Build system prompt using current relationships
    system_prompt = build_focused_system_prompt_v11(file_path, all_relationships, class_registry)
    
    # Extract class name from file path for context
    file_name = os.path.basename(file_path) if file_path else 'unknown'
    class_name = file_name.replace('.java', '') if file_name.endswith('.java') else file_name
    
    # Extract enhanced variable lineage with method context
    enhanced_var_lineage = extract_method_variable_context_v11(chunk.page_content, class_name)
    
    # Set file_path and application for enhanced variables
    app_name = 'ServiceBolt' if 'ServiceBolt' in str(file_path) else 'UnifiedBolt'
    for var_rel in enhanced_var_lineage:
        var_rel['file_path'] = file_path
        var_rel['application'] = app_name
    
    all_llm_lineage.extend(enhanced_var_lineage)
    
    # LLM Graph Transformer with current context
    transformer = LLMGraphTransformer(
        llm=llm,
        additional_instructions=system_prompt,
        allowed_nodes=['project', 'application', 'folder', 'file', 'class', 'method', 'interface', 'variable', 'endpoint', 'data'],
        allowed_relationships=[
            ('project', 'contains', 'application'),
            ('application', 'contains', 'folder'),
            ('folder', 'contains', 'folder'),
            ('folder', 'contains', 'file'),
            ('file', 'declares', 'class'),
            ('file', 'declares', 'interface'),
            ('class', 'declares', 'method'),
            ('class', 'declares_variable', 'variable'),
            ('class', 'exposes', 'endpoint'),
            ('class', 'extends', 'class'),
            ('class', 'implements', 'interface'),
            ('class', 'has_field', 'variable'),
            ('method', 'uses', 'variable'),
            ('variable', 'transforms_to', 'variable'),
            ('class', 'data_find', 'data'),
            ('method', 'data_find', 'data'),
        ],
        strict_mode=True,
        node_properties=False,
        relationship_properties=False,
    )
    
    try:
        graph_docs = transformer.convert_to_graph_documents([chunk])
        for gd in graph_docs:
            for rel in gd.relationships:
                s_node = rel.source.id.strip()
                s_type = rel.source.type.strip().lower()
                t_node = rel.target.id.strip()
                t_type = rel.target.type.strip().lower()
                rel_type = rel.type.strip().lower()

                # Normalize entities
                s_node = normalize_entity_v11(s_node, s_type)
                t_node = normalize_entity_v11(t_node, t_type)
                
                # Skip if empty or invalid
                if not s_node or not t_node or s_node == t_node:
                    continue
                
                # Determine application
                app_name = 'ServiceBolt' if 'ServiceBolt' in str(file_path) else 'UnifiedBolt'
                
                # Standardize variable names if they're variables
                if s_type == 'variable' and '.' not in s_node:
                    s_node = f"{class_name}.field.{s_node}"
                if t_type == 'variable' and '.' not in t_node:
                    t_node = f"{class_name}.field.{t_node}"
                
                all_llm_lineage.append({
                    'source_node': s_node,
                    'source_type': s_type,
                    'destination_node': t_node,
                    'destination_type': t_type,
                    'relationship': rel_type,
                    'file_path': file_path,
                    'application': app_name
                })
    except Exception as e:
        print(f'⚠️ LLM processing error for {file_path}: {e}')
        continue

# Add LLM relationships to master list
all_relationships.extend(all_llm_lineage)

print(f'✅ LLM processing complete. Added {len(all_llm_lineage)} relationships')
print(f'📊 Total relationships: {len(all_relationships)}')

if len(all_llm_lineage) > 0:
    llm_df = pd.DataFrame(all_llm_lineage)
    print(f'   🔗 LLM relationship types: {llm_df["relationship"].value_counts().to_dict()}')
    print(f'   🏢 LLM applications: {llm_df["application"].value_counts().to_dict()}')

# ========== STAGE 5: DATA CLEANING AND VALIDATION ==========

def clean_and_validate_data():
    '''Clean, validate, and prepare data for Neo4j'''
    print('🧹 Cleaning and validating data...')
    
    # Convert to DataFrame for easier processing
    df = pd.DataFrame(all_relationships)
    
    print(f'📊 Initial dataset: {len(df)} relationships')
    
    # 1. Handle missing values
    df['file_path'] = df['file_path'].fillna('')
    df['application'] = df['application'].fillna('Unknown')
    
    # 2. Remove duplicates
    initial_count = len(df)
    df = df.drop_duplicates(subset=['source_node', 'source_type', 'destination_node', 'destination_type', 'relationship'])
    print(f'🔄 Removed {initial_count - len(df)} duplicates')
    
    # 3. Validate node names
    def is_valid_node_name(name):
        if pd.isna(name) or str(name).strip() == '':
            return False
        return True
    
    valid_mask = (df['source_node'].apply(is_valid_node_name) & 
                  df['destination_node'].apply(is_valid_node_name))
    
    invalid_count = len(df) - valid_mask.sum()
    if invalid_count > 0:
        print(f'🔄 Removed {invalid_count} relationships with invalid node names')
        df = df[valid_mask]
    
    # 4. Standardize relationship types
    relationship_mapping = {
        'contains': 'CONTAINS',
        'declares': 'DECLARES',
        'declares_variable': 'DECLARES_VARIABLE',
        'extends': 'EXTENDS',
        'uses': 'USES',
        'transforms_to': 'TRANSFORMS_TO',
        'data_find': 'DATA_FIND'
    }
    
    df['neo4j_relationship'] = df['relationship'].map(relationship_mapping)
    df['neo4j_relationship'] = df['neo4j_relationship'].fillna(df['relationship'].str.upper())
    
    # 5. Add node labels for Neo4j
    node_type_mapping = {
        'project': 'Project',
        'application': 'Application',
        'folder': 'Folder',
        'file': 'File',
        'class': 'Class',
        'method': 'Method',
        'variable': 'Variable'
    }
    
    df['source_label'] = df['source_type'].map(node_type_mapping)
    df['destination_label'] = df['destination_type'].map(node_type_mapping)
    
    # 6. Generate statistics
    print('\n📊 Final Dataset Statistics:')
    print(f'   Total relationships: {len(df)}')
    print(f'   Relationship types: {df["relationship"].value_counts().to_dict()}')
    print(f'   Applications: {df["application"].value_counts().to_dict()}')
    print(f'   Node types: {df["source_type"].value_counts().to_dict()}')
    
    # 7. Validate standardized variables
    standardized_vars = df[df['destination_type'] == 'variable']['destination_node']
    properly_standardized = standardized_vars.str.contains(r'^[^.]+\.[^.]+\.[^.]+$', na=False)
    print(f'   Properly standardized variables: {properly_standardized.sum()}/{len(standardized_vars)}')
    
    # 8. Export cleaned data
    df.to_csv('oneinsights_v11_clean_relationships.csv', index=False)
    print(f'✅ Exported clean dataset to oneinsights_v11_clean_relationships.csv')
    
    return df

# Clean the data
df_clean = clean_and_validate_data()

# ========== STAGE 6: NEO4J LOADING WITH PROPER STRUCTURE ==========

def load_to_neo4j(df):
    '''Load clean data to Neo4j with proper structure'''
    if graph is None:
        print('❌ Neo4j connection not available')
        return
    
    try:
        print('🚀 Loading data to Neo4j...')
        
        # Clear existing data
        print('🧹 Clearing existing data...')
        graph.query('MATCH (n) DETACH DELETE n')
        
        # Load data in batches
        batch_size = 100
        total_batches = len(df) // batch_size + (1 if len(df) % batch_size > 0 else 0)
        
        print(f'📊 Loading {len(df)} relationships in {total_batches} batches...')
        
        for i in tqdm(range(0, len(df), batch_size), desc='Loading to Neo4j', total=total_batches):
            batch = df.iloc[i:i+batch_size]
            
            for _, row in batch.iterrows():
                try:
                    # Create Cypher query
                    cypher = f"""
                    MERGE (source:{row['source_label']} {{name: $source_name, application: $app}})
                    MERGE (target:{row['destination_label']} {{name: $target_name, application: $app}})
                    MERGE (source)-[:{row['neo4j_relationship']}]->(target)
                    """
                    
                    # Execute query
                    graph.query(cypher, {
                        'source_name': str(row['source_node']),
                        'target_name': str(row['destination_node']),
                        'app': str(row['application'])
                    })
                
                except Exception as e:
                    print(f'⚠️ Error loading relationship: {e}')
                    continue
        
        # Create indexes for better performance
        print('🔧 Creating indexes...')
        indexes = [
            'CREATE INDEX IF NOT EXISTS FOR (n:Project) ON (n.name)',
            'CREATE INDEX IF NOT EXISTS FOR (n:Application) ON (n.name)',
            'CREATE INDEX IF NOT EXISTS FOR (n:Class) ON (n.name)',
            'CREATE INDEX IF NOT EXISTS FOR (n:Method) ON (n.name)',
            'CREATE INDEX IF NOT EXISTS FOR (n:File) ON (n.name)',
            'CREATE INDEX IF NOT EXISTS FOR (n:Variable) ON (n.name)',
            'CREATE INDEX IF NOT EXISTS FOR (n:Folder) ON (n.name)'
        ]
        
        for index in indexes:
            try:
                graph.query(index)
            except Exception as e:
                print(f'⚠️ Index creation warning: {e}')
        
        print('✅ Data successfully loaded to Neo4j!')
        
        # Verify the load
        verify_neo4j_load()
        
    except Exception as e:
        print(f'❌ Error loading to Neo4j: {e}')

def verify_neo4j_load():
    '''Verify the Neo4j load and show statistics'''
    print('\n🔍 Verifying Neo4j load...')
    
    try:
        # Node statistics
        node_stats = graph.query("""
        MATCH (n)
        RETURN labels(n)[0] as node_type, count(*) as count
        ORDER BY count DESC
        """)
        
        print('📊 Node Statistics:')
        for stat in node_stats:
            print(f'   {stat["node_type"]}: {stat["count"]} nodes')
        
        # Relationship statistics
        rel_stats = graph.query("""
        MATCH ()-[r]->()
        RETURN type(r) as relationship_type, count(*) as count
        ORDER BY count DESC
        """)
        
        print('\n🔗 Relationship Statistics:')
        for stat in rel_stats:
            print(f'   {stat["relationship_type"]}: {stat["count"]} relationships')
        
        # Verify project structure
        project_structure = graph.query("""
        MATCH (p:Project)-[:CONTAINS]->(a:Application)
        RETURN p.name as project, collect(a.name) as applications
        """)
        
        print('\n🏗️ Project Structure:')
        for struct in project_structure:
            print(f'   {struct["project"]} → {struct["applications"]}')
        
        # Sample standardized variables
        sample_vars = graph.query("""
        MATCH (v:Variable)
        WHERE v.name CONTAINS '.'
        RETURN v.name as variable_name
        LIMIT 10
        """)
        
        print('\n🔗 Sample Standardized Variables:')
        for var in sample_vars:
            print(f'   {var["variable_name"]}')
        
    except Exception as e:
        print(f'❌ Error verifying Neo4j load: {e}')

# Load to Neo4j
load_to_neo4j(df_clean)

print('\n🎉 ONEINSIGHTS V11 ANALYSIS COMPLETE!')
print('\n✅ Achievements:')
print('   🏗️ Complete hierarchy: PROJECT → APPLICATION → FOLDER → FILE → CLASS → METHOD → VARIABLE')
print('   🔗 OneInsights project connects to BOTH applications')
print('   📊 Variables standardized as ClassName.methodName.variableName')
print('   🤖 LLM-enhanced semantic relationship extraction')
print('   🔍 AST + LLM combined analysis for comprehensive coverage')
print('   🧹 Clean, validated data loaded to Neo4j')
print('   📁 Complete CSV export for analysis')
print('\n🎯 Ready for advanced graph analysis and lineage tracking!')
print('\n📋 Processing Summary:')
print('   Stage 1: Setup and Configuration ✅')
print('   Stage 2: File Discovery and Hierarchy Building ✅')
print('   Stage 3: AST-based Code Analysis ✅')
print('   Stage 4: Variable Standardization and Connection Building ✅')
print('   Stage 4B: LLM Preparation with Current Context ✅')
print('   Stage 4C: LLM Processing with Current Context ✅')
print('   Stage 5: Data Cleaning and Validation ✅')
print('   Stage 6: Neo4j Loading with Proper Structure ✅')