# ========== IMPORTS AND SETUP ==========
import os
from pathlib import Path
from tqdm import tqdm
import pandas as pd
import re
from collections import defaultdict
import warnings
warnings.filterwarnings('ignore')

from tree_sitter import Language, Parser
import tree_sitter_java as tsjava

from langchain_community.document_loaders import TextLoader
from langchain.text_splitter import RecursiveCharacterTextSplitter, Language as LC_Language
from langchain_google_genai import ChatGoogleGenerativeAI
from langchain_experimental.graph_transformers import LLMGraphTransformer
from langchain_community.graphs import Neo4jGraph

# ========== CONFIGURATION ==========
BASE_PATH = Path(r'C:/Shaik/sample/OneInsights')
NEO4J_URI = 'bolt://localhost:7687'
NEO4J_USER = 'neo4j'
NEO4J_PASSWORD = 'Test@7889'
NEO4J_DB = 'oneinsights-v10'
GOOGLE_API_KEY = 'AIzaSyD8qIjUPsRbhJr59qoIIhczbPPKA2hter0'

# ========== INITIALIZE COMPONENTS ==========
try:
    graph = Neo4jGraph(url=NEO4J_URI, username=NEO4J_USER, password=NEO4J_PASSWORD, database=NEO4J_DB)
    print('✅ Neo4j connection established')
except Exception as e:
    print(f'❌ Neo4j connection failed: {e}')
    graph = None

JAVA_LANGUAGE = Language(tsjava.language())
parser = Parser(JAVA_LANGUAGE)

llm = ChatGoogleGenerativeAI(
    model='gemini-2.0-flash-exp',
    temperature=0,
    google_api_key=GOOGLE_API_KEY
)

# ========== FOCUSED FILTERING (from v9) ==========
NOISE_VARIABLES = {
    # Loop variables
    'i', 'j', 'k', 'l', 'm', 'n', 'x', 'y', 'z',
    # Common temporary variables
    'temp', 'tmp', 'obj', 'item', 'elem', 'node', 'val', 'value',
    # Common short variables
    'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w',
    # Reserved keywords
    'this', 'super', 'null', 'true', 'false', 'void', 'return',
    # Meaningless short names
    'it', 'ex', 'e1', 'e2', 'o1', 'o2'
}

APPLICATIONS = {
    'ServiceBolt': 'REST API Service Layer',
    'UnifiedBolt': 'Core Business Logic and Data Layer'
}

def is_meaningful_variable(var_name):
    '''Filter out noise variables and keep only meaningful ones (v9 focused filtering)'''
    if not var_name or len(var_name) < 2:
        return False
    
    if var_name.lower() in NOISE_VARIABLES:
        return False
    
    # Focus on business-meaningful variables (v9 enhancement)
    if len(var_name) > 4:  # Increased threshold for more meaningful names
        return True
    
    meaningful_short = {'id', 'url', 'api', 'key', 'dto', 'dao', 'req', 'res', 'ctx', 'data'}
    return var_name.lower() in meaningful_short

def detect_application(file_path):
    '''Detect which application a file belongs to'''
    path_str = str(file_path).replace('\\', '/')
    if 'ServiceBolt' in path_str:
        return 'ServiceBolt'
    elif 'UnifiedBolt' in path_str:
        return 'UnifiedBolt'
    return 'Unknown'

print('🚀 Setup complete! Ready for complete analysis with focused filtering...')

# ========== STAGE 1: ENHANCED FOLDER + FILE HIERARCHY (from v8 + v9 focus) ==========

def extract_enhanced_folder_file_hierarchy(base_path):
    '''Extract complete hierarchy with v9 focused structure: PROJECT -> APPLICATIONS -> FOLDERS -> FILES'''
    folder_records, file_records, app_records = [], [], []
    base_path = os.path.abspath(base_path)
    base_folder_name = os.path.basename(base_path)
    processed_folders = set()
    detected_apps = set()

    for root, dirs, files in os.walk(base_path):
        rel_root = os.path.relpath(root, base_path)
        app_name = detect_application(root)
        
        if app_name != 'Unknown':
            detected_apps.add(app_name)

        # Handle folder hierarchy with proper parent-child relationships
        if rel_root != '.':
            path_parts = rel_root.split(os.sep)
            for i in range(len(path_parts)):
                current_folder = path_parts[i]
                parent = path_parts[i-1] if i > 0 else None
                
                # Determine parent type and name (v9 focused approach)
                if parent is None:
                    # Top-level folder under application
                    if app_name != 'Unknown':
                        parent_name = app_name
                        parent_type = 'application'
                    else:
                        parent_name = base_folder_name
                        parent_type = 'project'
                else:
                    parent_name = parent
                    parent_type = 'folder'
                
                folder_key = f'{parent_name}->{current_folder}'
                if folder_key not in processed_folders:
                    folder_records.append({
                        'source_node': parent_name,
                        'source_type': parent_type,
                        'destination_node': current_folder,
                        'destination_type': 'folder',
                        'relationship': 'contains',
                        'file_path': None,
                        'application': app_name
                    })
                    processed_folders.add(folder_key)

        # Handle files
        current_folder = os.path.basename(root) if rel_root != '.' else base_folder_name
        for f in files:
            if f.endswith('.java'):
                file_rel_path = os.path.relpath(os.path.join(root, f), base_path)
                file_records.append({
                    'source_node': current_folder,
                    'source_type': 'folder' if rel_root != '.' else 'project',
                    'destination_node': f,
                    'destination_type': 'file',
                    'relationship': 'contains',
                    'file_path': file_rel_path,
                    'application': app_name
                })
    
    # Create PROJECT -> APPLICATION relationships (v9 focus)
    for app in detected_apps:
        app_records.append({
            'source_node': base_folder_name,
            'source_type': 'project',
            'destination_node': app,
            'destination_type': 'application',
            'relationship': 'contains',
            'file_path': None,
            'application': app
        })
    
    return folder_records, file_records, app_records

print('📁 Stage 1: Extracting enhanced folder and file hierarchy...')
folder_records, file_records, app_records = extract_enhanced_folder_file_hierarchy(BASE_PATH)
df_folders = pd.DataFrame(folder_records)
df_files = pd.DataFrame(file_records)
df_apps = pd.DataFrame(app_records)

print(f'✅ Stage 1 Complete: {len(df_folders)} folder relationships, {len(df_files)} file relationships, {len(df_apps)} applications')

# ========== STAGE 2: UTILITY FUNCTIONS (from v8) ==========

def read_source_code(file_path):
    '''Read source code file'''
    with open(file_path, 'r', encoding='utf-8') as f:
        return f.read().encode('utf-8')

def extract_package_and_imports(source_code_str):
    '''Extract package name and imports from Java source'''
    package_pattern = r'package\s+([\w\.]+);'
    import_pattern = r'import\s+([\w\.]+);'
    package_match = re.search(package_pattern, source_code_str)
    package_name = package_match.group(1) if package_match else None
    import_matches = re.findall(import_pattern, source_code_str)
    return package_name, import_matches

def extract_api_endpoints(source_code_str):
    '''Extract REST API endpoints from Spring annotations'''
    endpoints = []
    mapping_patterns = {
        'RequestMapping': [
            r'@RequestMapping\s*\(\s*value\s*=\s*["\']([^"\']+)["\']',
            r'@RequestMapping\s*\(\s*["\']([^"\']+)["\']',
            r'@RequestMapping\s*\(\s*path\s*=\s*["\']([^"\']+)["\']'
        ],
        'GetMapping': [
            r'@GetMapping\s*\(\s*["\']([^"\']+)["\']',
            r'@GetMapping\s*\(\s*value\s*=\s*["\']([^"\']+)["\']'
        ],
        'PostMapping': [
            r'@PostMapping\s*\(\s*["\']([^"\']+)["\']',
            r'@PostMapping\s*\(\s*value\s*=\s*["\']([^"\']+)["\']'
        ],
        'PutMapping': [
            r'@PutMapping\s*\(\s*["\']([^"\']+)["\']'
        ],
        'DeleteMapping': [
            r'@DeleteMapping\s*\(\s*["\']([^"\']+)["\']'
        ]
    }
    
    for mapping_type, patterns in mapping_patterns.items():
        for pattern in patterns:
            matches = re.findall(pattern, source_code_str, re.MULTILINE | re.DOTALL)
            for match in matches:
                if match.strip():
                    endpoints.append({
                        'type': mapping_type,
                        'path': match.strip(),
                        'method': mapping_type.replace('Mapping', '').upper() if mapping_type != 'RequestMapping' else 'GET'
                    })
    
    return endpoints

def extract_database_entities(source_code_str):
    '''Extract database entities from JPA annotations'''
    entities = []
    
    # Entity detection
    entity_patterns = [
        r'@Entity\s*(?:\([^)]*\))?',
        r'@Table\s*\(\s*name\s*=\s*["\']([^"\']+)["\']'
    ]
    
    for pattern in entity_patterns:
        if re.search(pattern, source_code_str, re.MULTILINE):
            table_matches = re.findall(r'@Table\s*\(\s*name\s*=\s*["\']([^"\']+)["\']', source_code_str)
            for table_name in table_matches:
                if table_name.strip():
                    entities.append({
                        'type': 'table',
                        'name': table_name.strip()
                    })
    
    # Repository pattern detection
    repository_pattern = r'interface\s+(\w+)\s+extends\s+.*Repository'
    repo_matches = re.findall(repository_pattern, source_code_str)
    for repo_name in repo_matches:
        entity_name = repo_name.replace('Repository', '').replace('Rep', '')
        if entity_name:
            entities.append({
                'type': 'table',
                'name': entity_name.lower()
            })
    
    return entities

def extract_class_relationships(source_code_str):
    '''Extract class inheritance relationships (v9 focused: prioritize EXTENDS)'''
    relationships = []
    
    # Class extends (prioritized in v9)
    class_extends_pattern = r'class\s+(\w+)\s+extends\s+([\w<>]+)'
    class_matches = re.findall(class_extends_pattern, source_code_str)
    for child_class, parent_class in class_matches:
        parent_class = re.sub(r'<.*?>', '', parent_class).strip()
        if parent_class:
            relationships.append({
                'child_class': child_class,
                'parent_class': parent_class,
                'relationship_type': 'extends'
            })
    
    # Class implements (minimal as per v9 preference)
    implements_pattern = r'class\s+(\w+)(?:\s+extends\s+\w+)?\s+implements\s+([\w<>,\s]+)'
    impl_matches = re.findall(implements_pattern, source_code_str)
    for class_name, implements_clause in impl_matches:
        interfaces = [part.strip().split('<')[0].strip() for part in implements_clause.split(',')]
        for interface in interfaces:
            if interface and len(interface) > 8:  # Only meaningful interfaces (v9 filter)
                relationships.append({
                    'child_class': class_name,
                    'parent_class': interface,
                    'relationship_type': 'implements'
                })
    
    return relationships

print('🔧 Stage 2: Utility functions loaded')

# ========== STAGE 3: FOCUSED DATA LINEAGE PATTERNS (from v8 + v9 focus) ==========

def extract_focused_data_lineage_patterns(source_code_str):
    '''Extract data lineage patterns with v9 focused filtering'''
    lineage_records = []
    
    # 1. SQL Query patterns - Data Sources (focused on meaningful operations)
    sql_patterns = [
        r'@Query\s*\(\s*["\']([^"\']*(SELECT|INSERT|UPDATE|DELETE)[^"\']*)["\']',
        r'@Query\s*\(\s*value\s*=\s*["\']([^"\']*(SELECT|INSERT|UPDATE|DELETE)[^"\']*)["\']',
        r'@Query\s*\(\s*nativeQuery\s*=\s*true\s*,\s*value\s*=\s*["\']([^"\']*(SELECT|INSERT|UPDATE|DELETE)[^"\']*)["\']'
    ]
    
    for pattern in sql_patterns:
        matches = re.findall(pattern, source_code_str, re.MULTILINE | re.IGNORECASE | re.DOTALL)
        for match in matches:
            if isinstance(match, tuple):
                query = match[0]
                operation = match[1].upper()
            else:
                query = match
                operation = 'SELECT'
            
            # Extract table names from SQL
            table_patterns = [
                r'FROM\s+(\w+)',
                r'JOIN\s+(\w+)',
                r'UPDATE\s+(\w+)',
                r'INSERT\s+INTO\s+(\w+)',
                r'DELETE\s+FROM\s+(\w+)'
            ]
            
            for table_pattern in table_patterns:
                table_matches = re.findall(table_pattern, query, re.IGNORECASE)
                for table_name in table_matches:
                    lineage_records.append({
                        'source_type': 'data',
                        'source_name': table_name.lower(),
                        'target_type': 'method',
                        'target_name': f'{operation}_operation',
                        'operation': operation,
                        'lineage_type': 'data_find'
                    })
    
    # 2. Focused Data Transformation patterns (v9 meaningful variables only)
    transformation_patterns = [
        # Service method calls that transform data
        r'(\w{5,})\s*=\s*(\w+Service)\s*\.\s*(\w+)\s*\(',
        # Repository operations
        r'(\w{5,})\s*=\s*(\w+Repository)\s*\.\s*(find|save|delete)\w*\s*\(',
        # Stream transformations (meaningful variables only)
        r'(\w{5,})\s*=\s*(\w{5,})\.stream\s*\(\s*\)\.(?:map|filter|collect)\s*\(',
        # Mapper transformations
        r'(\w{5,})\s*=\s*\w*[Mm]apper\s*\.\s*\w+\s*\(\s*(\w{5,})'
    ]
    
    for pattern in transformation_patterns:
        matches = re.findall(pattern, source_code_str, re.MULTILINE)
        for match in matches:
            if len(match) >= 2:
                target_var = match[0]
                source_identifier = match[1]
                operation = match[2] if len(match) >= 3 else 'transform'
                
                if (is_meaningful_variable(target_var) and 
                    is_meaningful_variable(source_identifier) and 
                    target_var != source_identifier):
                    
                    lineage_records.append({
                        'source_type': 'variable',
                        'source_name': source_identifier,
                        'target_type': 'variable',
                        'target_name': target_var,
                        'operation': operation,
                        'lineage_type': 'transforms_to'
                    })
    
    return lineage_records

def build_focused_data_lineage_graph(source_code_str, class_name, app_name):
    '''Build focused data lineage graph for a class'''
    all_lineage = extract_focused_data_lineage_patterns(source_code_str)
    
    # Add class and application context
    for record in all_lineage:
        record['class_name'] = class_name
        record['application'] = app_name
    
    return all_lineage

print('📊 Stage 3: Focused data lineage patterns loaded')

# ========== STAGE 4: ENHANCED CLASS REGISTRY WITH FOCUSED METADATA (from v8 + v9) ==========

def build_enhanced_focused_class_registry():
    '''Build comprehensive class registry with focused metadata filtering'''
    class_registry = {}
    inter_app_calls = []
    
    print('🔍 Stage 4: Building enhanced class registry with focused filtering...')
    java_files = list(BASE_PATH.rglob('*.java'))
    
    for file_path in tqdm(java_files, desc='Processing files'):
        app_name = detect_application(file_path)
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                source_code_str = f.read()
            
            package_name, imports = extract_package_and_imports(source_code_str)
            endpoints = extract_api_endpoints(source_code_str)
            db_entities = extract_database_entities(source_code_str)
            class_relationships = extract_class_relationships(source_code_str)
            
            class_name = file_path.stem
            fqcn = f'{package_name}.{class_name}' if package_name else class_name
            
            # Extract focused data lineage
            data_lineage = build_focused_data_lineage_graph(source_code_str, class_name, app_name)
            
            # Detect inter-application dependencies
            for imp in imports:
                if app_name == 'ServiceBolt' and 'UnifiedBolt' in imp:
                    inter_app_calls.append({
                        'from_app': 'ServiceBolt',
                        'to_app': 'UnifiedBolt',
                        'from_class': class_name,
                        'imported_class': imp.split('.')[-1],
                        'import_type': 'dependency'
                    })
                elif app_name == 'UnifiedBolt' and 'ServiceBolt' in imp:
                    inter_app_calls.append({
                        'from_app': 'UnifiedBolt',
                        'to_app': 'ServiceBolt',
                        'from_class': class_name,
                        'imported_class': imp.split('.')[-1],
                        'import_type': 'dependency'
                    })
            
            class_registry[class_name] = {
                'fqcn': fqcn,
                'package': package_name,
                'file_path': str(file_path),
                'application': app_name,
                'imports': imports,
                'endpoints': endpoints,
                'db_entities': db_entities,
                'class_relationships': class_relationships,
                'data_lineage': data_lineage
            }
            
        except Exception as e:
            print(f'❌ Error processing {file_path.name}: {e}')
            continue
    
    return class_registry, inter_app_calls

class_registry, inter_app_calls = build_enhanced_focused_class_registry()

# Summary statistics
total_endpoints = sum(len(info.get('endpoints', [])) for info in class_registry.values())
total_db_entities = sum(len(info.get('db_entities', [])) for info in class_registry.values())
total_data_lineage = sum(len(info.get('data_lineage', [])) for info in class_registry.values())

print(f'✅ Stage 4 Complete:')
print(f'   📁 {len(class_registry)} classes processed')
print(f'   🌐 {total_endpoints} API endpoints found')
print(f'   🗄️ {total_db_entities} database entities found')
print(f'   📊 {total_data_lineage} focused data lineage patterns found')
print(f'   🔗 {len(inter_app_calls)} inter-application dependencies')

# ========== STAGE 5: ENHANCED AST EXTRACTION WITH FOCUSED FILTERING (from v8 + v9) ==========

def extract_enhanced_variable_transformations(source_code_str):
    '''Extract variable transformations with v9 focused filtering'''
    transformations = []
    
    assignment_patterns = [
        r'(\w{5,})\s*=\s*(\w{5,})\s*[+\-*/]\s*(\w{5,})',  # binary operations (meaningful vars only)
        r'(\w{5,})\s*=\s*(\w{5,})\s*\.\s*(\w+)\s*\(',  # method calls
        r'(\w{5,})\s*=\s*new\s+(\w+)\s*\(',  # object creation
    ]
    
    for pattern in assignment_patterns:
        matches = re.findall(pattern, source_code_str, re.MULTILINE)
        for match in matches:
            if len(match) >= 3:
                result_var = match[0]
                input_vars = [match[1], match[2]]
                
                if (is_meaningful_variable(result_var) and 
                    all(is_meaningful_variable(var) for var in input_vars)):
                    transformations.append({
                        'result': result_var,
                        'inputs': input_vars,
                        'operation': 'transforms_to'
                    })
    
    return transformations

def extract_enhanced_focused_ast_structure(file_path):
    '''Extract AST structure with v8 completeness and v9 focused filtering'''
    records = []
    source_code = read_source_code(file_path)
    source_code_str = source_code.decode('utf-8')
    tree = parser.parse(source_code)
    root_node = tree.root_node
    file_name = os.path.basename(file_path)
    app_name = detect_application(file_path)
    
    transformations = extract_enhanced_variable_transformations(source_code_str)

    def clean_node_name(name):
        '''Clean node names'''
        if not name:
            return name
        
        prefixes_to_remove = ['method:', 'class:', 'variable:', 'field:']
        for prefix in prefixes_to_remove:
            if name.lower().startswith(prefix):
                name = name[len(prefix):]
        
        name = re.sub(r'\.(java|class)$', '', name, flags=re.IGNORECASE)
        return name.strip()

    def traverse(node, parent_type=None, parent_name=None, context='global'):
        # Handle class declarations
        if node.type == 'class_declaration':
            class_name = None
            for child in node.children:
                if child.type == 'identifier':
                    class_name = clean_node_name(source_code[child.start_byte:child.end_byte].decode('utf-8'))
                    
                    # File -> Class relationship
                    records.append({
                        'source_node': file_name,
                        'source_type': 'file',
                        'destination_node': class_name,
                        'destination_type': 'class',
                        'relationship': 'declares',
                        'file_path': str(file_path),
                        'application': app_name
                    })
                    
                    # Add metadata from registry with v9 focused approach
                    class_info = class_registry.get(class_name, {})
                    
                    # API endpoints (EXPOSES relationship)
                    for ep in class_info.get('endpoints', []):
                        endpoint_name = f"{ep['method']} {ep['path']}"
                        records.append({
                            'source_node': class_name,
                            'source_type': 'class',
                            'destination_node': endpoint_name,
                            'destination_type': 'endpoint',
                            'relationship': 'exposes',
                            'file_path': str(file_path),
                            'application': app_name
                        })
                    
                    # Database entities (DATA_FIND relationship)
                    for entity in class_info.get('db_entities', []):
                        records.append({
                            'source_node': class_name,
                            'source_type': 'class',
                            'destination_node': entity['name'],
                            'destination_type': 'data',
                            'relationship': 'data_find',
                            'file_path': str(file_path),
                            'application': app_name
                        })
                    
                    # Class relationships (v9 focused: prioritize EXTENDS)
                    for rel in class_info.get('class_relationships', []):
                        if rel['child_class'] == class_name:
                            if rel['relationship_type'] == 'extends':
                                records.append({
                                    'source_node': class_name,
                                    'source_type': 'class',
                                    'destination_node': rel['parent_class'],
                                    'destination_type': 'class',
                                    'relationship': 'extends',
                                    'file_path': str(file_path),
                                    'application': app_name
                                })
                            # Minimal IMPLEMENTS (v9 preference)
                            elif rel['relationship_type'] == 'implements' and len(rel['parent_class']) > 8:
                                records.append({
                                    'source_node': class_name,
                                    'source_type': 'class',
                                    'destination_node': rel['parent_class'],
                                    'destination_type': 'interface',
                                    'relationship': 'implements',
                                    'file_path': str(file_path),
                                    'application': app_name
                                })
                    
                    # Data lineage (focused)
                    for lineage in class_info.get('data_lineage', []):
                        records.append({
                            'source_node': lineage['source_name'],
                            'source_type': lineage['source_type'],
                            'destination_node': lineage['target_name'],
                            'destination_type': lineage['target_type'],
                            'relationship': lineage['lineage_type'],
                            'file_path': str(file_path),
                            'application': app_name
                        })
                    break
            
            # Traverse children with class context
            for child in node.children:
                traverse(child, 'class', class_name, 'class')
                
        # Handle method declarations
        elif node.type == 'method_declaration':
            method_name = None
            for child in node.children:
                if child.type == 'identifier':
                    method_name = clean_node_name(source_code[child.start_byte:child.end_byte].decode('utf-8'))
                    
                    if parent_name and parent_type in ['class', 'interface']:
                        records.append({
                            'source_node': parent_name,
                            'source_type': parent_type,
                            'destination_node': method_name,
                            'destination_type': 'method',
                            'relationship': 'declares',
                            'file_path': str(file_path),
                            'application': app_name
                        })
                    break
            
            for child in node.children:
                traverse(child, 'method', method_name, 'method')
                
        # Handle field declarations with v9 focused filtering
        elif node.type == 'field_declaration':
            for child in node.children:
                if child.type == 'variable_declarator':
                    for grandchild in child.children:
                        if grandchild.type == 'identifier':
                            field_name = clean_node_name(source_code[grandchild.start_byte:grandchild.end_byte].decode('utf-8'))
                            
                            if is_meaningful_variable(field_name) and parent_name and parent_type == 'class':
                                records.append({
                                    'source_node': parent_name,
                                    'source_type': parent_type,
                                    'destination_node': field_name,
                                    'destination_type': 'variable',
                                    'relationship': 'has_field',
                                    'file_path': str(file_path),
                                    'application': app_name
                                })
        else:
            # Continue traversing
            for child in node.children:
                traverse(child, parent_type, parent_name, context)

    traverse(root_node)
    
    # Add focused transformation relationships
    for transform in transformations:
        for input_var in transform['inputs']:
            records.append({
                'source_node': input_var,
                'source_type': 'variable',
                'destination_node': transform['result'],
                'destination_type': 'variable',
                'relationship': 'transforms_to',
                'file_path': str(file_path),
                'application': app_name
            })
    
    return records

# Execute enhanced AST extraction
print('🌳 Stage 5: Extracting enhanced AST structures with focused filtering...')
ast_records = []
java_files = list(BASE_PATH.rglob('*.java'))

for file_path in tqdm(java_files, desc='Enhanced AST Processing'):
    try:
        ast_records.extend(extract_enhanced_focused_ast_structure(file_path))
    except Exception as e:
        print(f'❌ Error processing {file_path.name}: {e}')
        continue

df_ast = pd.DataFrame(ast_records)
print(f'✅ Stage 5 Complete: {len(df_ast)} enhanced relationships extracted')
if len(df_ast) > 0:
    print(f'   📊 Relationship types: {df_ast["relationship"].value_counts().to_dict()}')
    print(f'   🏢 Applications: {df_ast["application"].value_counts().to_dict()}')

# ========== STAGE 6: LLM EXTRACTION WITH FOCUSED CONTEXT (from v8 + v9) ==========

def build_focused_system_prompt(file_path, ast_df, class_registry):
    '''Build system prompt with focused context from v9'''
    ast_subset = ast_df[ast_df['file_path'] == file_path] if len(ast_df) > 0 else pd.DataFrame()
    ast_context = ''
    for _, row in ast_subset.iterrows():
        ast_context += f"{row['source_type']}:{row['source_node']} -[{row['relationship']}]-> {row['destination_type']}:{row['destination_node']}\n"
    
    registry_context = 'Known Classes:\n'
    for class_name, info in class_registry.items():
        registry_context += f'- {class_name} (FQCN: {info["fqcn"]})\n'
        if len(info.get('endpoints', [])) > 0:
            registry_context += f'  * {len(info["endpoints"])} API endpoint(s)\n'
        if len(info.get('db_entities', [])) > 0:
            registry_context += f'  * {len(info["db_entities"])} DB entity/entities\n'
    
    prompt = f"""
You are a Java code lineage extraction engine. Extract relationships between code entities with FOCUSED approach:

CONTEXT:
{registry_context}

AST RELATIONSHIPS (FOLLOW THESE PATTERNS EXACTLY):
{ast_context}

CRITICAL RULES - FOCUSED EXTRACTION (v9 approach):
1. Use SIMPLE names only (remove prefixes like "method:", "class:", etc.)
2. MANDATORY FOCUSED RELATIONSHIP DIRECTIONS:
   - project -[contains]-> application
   - application -[contains]-> folder
   - folder -[contains]-> file
   - file -[declares]-> class
   - class -[declares]-> method  
   - class -[has_field]-> variable (meaningful variables only)
   - class -[exposes]-> endpoint
   - class -[extends]-> class (prioritized)
   - class -[implements]-> interface (minimal)
   - class -[data_find]-> data
   - variable -[transforms_to]-> variable (meaningful only)
3. Extract REST API endpoints as 'endpoint' nodes (GET /api/users, POST /api/data)
4. Extract database tables/entities as 'data' nodes
5. Focus on MEANINGFUL variables only (length > 4 or business-relevant)
6. Prioritize EXTENDS over IMPLEMENTS relationships
7. Focus on global state changes over local variables
8. Clean node names (remove "method:", "class:" prefixes)

Extract triples in format:
[SourceType]:SourceName -[RELATIONSHIP]-> [TargetType]:TargetName

Return ONLY the triples, no explanations.
"""
    return prompt

def extract_focused_enhanced_variable_lineage(source_code_str, class_name, method_context=None):
    '''Extract variable lineage with v8 method context and v9 focused filtering'''
    variable_lineage = []
    
    # Enhanced variable assignment patterns with focused filtering
    assignment_patterns = [
        # Method return assignments: result = methodName(...) - meaningful vars only
        r'(\w{5,})\s*=\s*(\w{5,})\s*\(.*?\)',
        # Service/Repository calls: result = serviceInstance.methodName(...)
        r'(\w{5,})\s*=\s*(\w{5,})\s*\.\s*(\w+)\s*\(',
        # Object creation: result = new ClassName(...)
        r'(\w{5,})\s*=\s*new\s+(\w+)\s*\(',
        # Field access: result = object.fieldName
        r'(\w{5,})\s*=\s*(\w{5,})\s*\.\s*(\w+)(?!\s*\()',
        # Simple assignments: result = variable (meaningful only)
        r'(\w{5,})\s*=\s*(\w{5,})(?!\s*[\(\.])',
        # Stream operations: result = source.stream().operation(...)
        r'(\w{5,})\s*=\s*(\w{5,})\.stream\(\)\.(\w+)\(',
    ]
    
    for pattern in assignment_patterns:
        matches = re.findall(pattern, source_code_str, re.MULTILINE)
        for match in matches:
            if len(match) >= 2:
                result_var = match[0]
                source_identifier = match[1]
                operation = match[2] if len(match) >= 3 else 'assign'
                
                if (is_meaningful_variable(result_var) and 
                    is_meaningful_variable(source_identifier) and
                    result_var != source_identifier):
                    
                    # Create enhanced variable name with context (v8 approach)
                    result_with_context = f"{class_name}.{method_context or 'field'}.{result_var}" if method_context else f"{class_name}.{result_var}"
                    source_with_context = f"{class_name}.{method_context or 'field'}.{source_identifier}" if method_context else f"{class_name}.{source_identifier}"
                    
                    variable_lineage.append({
                        'source_node': source_with_context,
                        'source_type': 'variable',
                        'destination_node': result_with_context,
                        'destination_type': 'variable',
                        'relationship': 'transforms_to',
                        'operation_context': operation,
                        'class_context': class_name,
                        'method_context': method_context
                    })
    
    return variable_lineage

def extract_focused_method_variable_context(source_code_str, class_name):
    '''Extract method-level variable context with v9 focused filtering'''
    method_variables = []
    
    # Find method declarations and their variables
    method_pattern = r'(?:public|private|protected)?\s*(?:static)?\s*(?:\w+\s+)?(\w{3,})\s*\([^)]*\)\s*\{([^}]*(?:\{[^}]*\}[^}]*)*)\}'
    method_matches = re.findall(method_pattern, source_code_str, re.MULTILINE | re.DOTALL)
    
    for method_name, method_body in method_matches:
        if is_meaningful_variable(method_name):  # Only meaningful method names
            # Extract variables within this method
            method_var_lineage = extract_focused_enhanced_variable_lineage(method_body, class_name, method_name)
            method_variables.extend(method_var_lineage)
    
    return method_variables

# Prepare for LLM extraction
splitter = RecursiveCharacterTextSplitter.from_language(
    language=LC_Language.JAVA,
    chunk_size=4000,
    chunk_overlap=200
)

java_docs, split_docs = [], []
for root, _, files in os.walk(BASE_PATH):
    for file in files:
        if file.endswith('.java'):
            try:
                loader = TextLoader(os.path.join(root, file))
                java_docs.extend(loader.load())
            except Exception as e:
                print(f" Error loading {file}: {e}")
                continue

for doc in java_docs:
    split_docs.extend(splitter.split_documents([doc]))

print(f'📄 Stage 6: Prepared {len(split_docs)} document chunks for LLM processing')

# ========== STAGE 7: FOCUSED LLM PROCESSING (from v8 + v9) ==========

# Initialize focused LLM lineage collection
all_llm_lineage = []

print('🤖 Stage 7: Starting focused LLM extraction with enhanced variable context...')

for chunk in tqdm(split_docs, desc='Focused LLM Extraction'):
    file_path = chunk.metadata.get('source')
    system_prompt = build_focused_system_prompt(file_path, df_ast, class_registry)
    
    # Extract class name from file path for context
    file_name = os.path.basename(file_path) if file_path else 'unknown'
    class_name = file_name.replace('.java', '') if file_name.endswith('.java') else file_name
    
    # Extract focused enhanced variable lineage with method context
    enhanced_var_lineage = extract_focused_method_variable_context(chunk.page_content, class_name)
    all_llm_lineage.extend(enhanced_var_lineage)
    
    transformer = LLMGraphTransformer(
        llm=llm,
        additional_instructions=system_prompt,
        allowed_nodes=['project', 'application', 'folder', 'file', 'class', 'method', 'interface', 'variable', 'endpoint', 'data'],
        allowed_relationships=[
            ('project', 'contains', 'application'),
            ('application', 'contains', 'folder'),
            ('folder', 'contains', 'folder'),
            ('folder', 'contains', 'file'),
            ('file', 'declares', 'class'),
            ('file', 'declares', 'interface'),
            ('class', 'declares', 'method'),
            ('class', 'declares_variable', 'variable'),
            ('class', 'exposes', 'endpoint'),
            ('class', 'extends', 'class'),
            ('class', 'implements', 'interface'),
            ('class', 'has_field', 'variable'),
            ('method', 'uses', 'variable'),
            ('variable', 'transforms_to', 'variable'),
            ('class', 'data_find', 'data'),
            ('method', 'data_find', 'data'),
        ],
        strict_mode=True,
        node_properties=False,
        relationship_properties=False,
    )
    
    try:
        graph_docs = transformer.convert_to_graph_documents([chunk])
        for gd in graph_docs:
            for rel in gd.relationships:
                s_node = rel.source.id.strip()
                s_type = rel.source.type.strip().lower()
                t_node = rel.target.id.strip()
                t_type = rel.target.type.strip().lower()
                rel_type = rel.type.strip().lower()

                def normalize_entity(entity_name, entity_type):
                    if not entity_name:
                        return entity_name
                    
                    # Remove prefixes
                    prefixes = ['method:', 'class:', 'variable:', 'field:', 'table:', 'endpoint:']
                    for prefix in prefixes:
                        if entity_name.lower().startswith(prefix):
                            entity_name = entity_name[len(prefix):]
                    
                    # Remove file extensions
                    entity_name = re.sub(r'\.(java|class)$', '', entity_name, flags=re.IGNORECASE)
                    
                    # Clean dots for class names
                    if entity_type in ['class', 'method'] and '.' in entity_name:
                        entity_name = entity_name.split('.')[-1]
                    
                    # Match with class registry for consistency
                    if entity_type == 'class':
                        for class_name in class_registry.keys():
                            if entity_name.lower() == class_name.lower():
                                return class_name.lower()
                    
                    return entity_name.lower()

                s_node = normalize_entity(s_node, s_type)
                t_node = normalize_entity(t_node, t_type)

                # Skip invalid relationships
                if s_node == t_node and s_type == t_type:
                    continue
                if not s_node or not t_node:
                    continue
                
                # Enforce focused relationship directions (v9 approach)
                valid_directions = {
                    ('project', 'contains', 'application'),
                    ('application', 'contains', 'folder'),
                    ('folder', 'contains', 'folder'),
                    ('folder', 'contains', 'file'),
                    ('file', 'declares', 'class'),
                    ('file', 'declares', 'interface'),
                    ('class', 'declares', 'method'),
                    ('class', 'declares_variable', 'variable'),
                    ('class', 'exposes', 'endpoint'),
                    ('class', 'has_field', 'variable'),
                    ('method', 'uses', 'variable'),
                    ('class', 'extends', 'class'),
                    ('class', 'implements', 'interface'),
                    ('variable', 'transforms_to', 'variable'),
                    ('class', 'data_find', 'data'),
                    ('method', 'data_find', 'data')
                }
                
                if (s_type, rel_type, t_type) not in valid_directions:
                    continue

                all_llm_lineage.append({
                    'source_node': s_node,
                    'source_type': s_type,
                    'destination_node': t_node,
                    'destination_type': t_type,
                    'relationship': rel_type,
                    'file_path': file_path,
                    'application': detect_application(file_path) if file_path else 'Unknown'
                })
    except Exception as e:
        continue

df_llm = pd.DataFrame(all_llm_lineage)
print(f'✅ Stage 7 Complete: {len(df_llm)} focused LLM relationships extracted')
if len(df_llm) > 0:
    print(f'   📊 Enhanced variable lineage: {len([r for r in all_llm_lineage if "transforms_to" in r.get("relationship", "")])}')
    print(f'   🔗 Traditional relationships: {len([r for r in all_llm_lineage if "transforms_to" not in r.get("relationship", "")])}')
    print(f'   🏢 Applications: {df_llm["application"].value_counts().to_dict()}')

# ========== STAGE 8: FINAL DATA COMBINATION AND NEO4J LOADING (from v8 + v9) ==========

def combine_all_focused_enhanced_data():
    '''Combine all data sources with v8 completeness and v9 focused filtering'''
    all_records = []
    
    # Add hierarchy records (PROJECT -> APPLICATION -> FOLDER -> FILE)
    if 'df_folders' in globals() and len(df_folders) > 0:
        all_records.extend(df_folders.to_dict('records'))
        print(f'📁 Added {len(df_folders)} folder relationships')
    
    if 'df_files' in globals() and len(df_files) > 0:
        all_records.extend(df_files.to_dict('records'))
        print(f'📄 Added {len(df_files)} file relationships')
    
    if 'df_apps' in globals() and len(df_apps) > 0:
        all_records.extend(df_apps.to_dict('records'))
        print(f'🏢 Added {len(df_apps)} application relationships')
    
    # Add LLM records (focused extraction)
    if 'df_llm' in globals() and len(df_llm) > 0:
        all_records.extend(df_llm.to_dict('records'))
        print(f'🤖 Added {len(df_llm)} focused LLM relationships')
    
    # Add inter-application dependencies
    for call in inter_app_calls:
        all_records.append({
            'source_node': call['from_class'],
            'source_type': 'class',
            'destination_node': call['imported_class'],
            'destination_type': 'class',
            'relationship': 'uses',
            'file_path': None,
            'application': call['from_app']
        })
    
    print(f'🔗 Added {len(inter_app_calls)} inter-application dependencies')
    
    return pd.DataFrame(all_records)

def load_to_focused_neo4j(df_final):
    '''Load data to Neo4j with v9 focused node types and relationships'''
    if graph is None:
        print('❌ Neo4j connection not available')
        return
    
    try:
        # Clear existing data
        print('🧹 Clearing existing data...')
        graph.query('MATCH (n) DETACH DELETE n')
        
        # Create nodes and relationships with v9 focused labels
        print('📊 Loading focused enhanced data...')
        
        # Map our node types to Neo4j labels (v9 focused)
        node_type_mapping = {
            'project': 'Project',
            'application': 'Application', 
            'folder': 'Folder',
            'file': 'File',
            'class': 'Class',
            'method': 'Method',
            'interface': 'Interface',
            'variable': 'Variable',
            'endpoint': 'Endpoint',
            'data': 'Data'
        }
        
        # Map our relationships to Neo4j relationship types (v9 focused)
        relationship_mapping = {
            'contains': 'CONTAINS',
            'declares': 'DECLARES',
            'declares_variable': 'DECLARES_VARIABLE',
            'exposes': 'EXPOSES',
            'extends': 'EXTENDS',
            'implements': 'IMPLEMENTS',
            'has_field': 'HAS_FIELD',
            'uses': 'USES',
            'transforms_to': 'TRANSFORMS_TO',
            'data_find': 'DATA_FIND'
        }
        
        # Group by relationship type for efficient loading
        relationship_groups = df_final.groupby('relationship')
        
        for rel_type, group in tqdm(relationship_groups, desc='Loading relationships'):
            for _, row in group.iterrows():
                try:
                    source_label = node_type_mapping.get(row['source_type'], row['source_type'].title())
                    target_label = node_type_mapping.get(row['destination_type'], row['destination_type'].title())
                    neo4j_rel_type = relationship_mapping.get(rel_type, rel_type.upper().replace(' ', '_'))
                    
                    # Create nodes and relationship
                    cypher = f"""
                    MERGE (source:{source_label} {{name: $source_name, application: $app}})
                    MERGE (target:{target_label} {{name: $target_name, application: $app}})
                    MERGE (source)-[:{neo4j_rel_type}]->(target)
                    """
                    
                    graph.query(cypher, {
                        'source_name': str(row['source_node']),
                        'target_name': str(row['destination_node']),
                        'app': str(row['application'])
                    })
                
                except Exception as e:
                    print(f'⚠️ Error loading relationship: {e}')
                    continue
        
        # Create indexes for performance
        indexes = [
            'CREATE INDEX IF NOT EXISTS FOR (n:Project) ON (n.name)',
            'CREATE INDEX IF NOT EXISTS FOR (n:Application) ON (n.name)',
            'CREATE INDEX IF NOT EXISTS FOR (n:Class) ON (n.name)',
            'CREATE INDEX IF NOT EXISTS FOR (n:Method) ON (n.name)',
            'CREATE INDEX IF NOT EXISTS FOR (n:File) ON (n.name)',
            'CREATE INDEX IF NOT EXISTS FOR (n:Data) ON (n.name)'
        ]
        
        for index in indexes:
            try:
                graph.query(index)
            except:
                pass
        
        print('✅ Focused enhanced data successfully loaded to Neo4j')
        
        # Get statistics
        stats = graph.query("""
        MATCH (n)
        RETURN labels(n)[0] as node_type, count(*) as count
        ORDER BY count DESC
        """)
        
        print('\n📊 Neo4j Node Statistics:')
        for stat in stats:
            print(f'   {stat["node_type"]}: {stat["count"]} nodes')
        
        # Get relationship statistics
        rel_stats = graph.query("""
        MATCH ()-[r]->()
        RETURN type(r) as relationship_type, count(*) as count
        ORDER BY count DESC
        """)
        
        print('\n🔗 Neo4j Relationship Statistics:')
        for stat in rel_stats:
            print(f'   {stat["relationship_type"]}: {stat["count"]} relationships')
    
    except Exception as e:
        print(f'❌ Error loading to Neo4j: {e}')

# Execute final data combination and loading
print('\n🔄 Stage 8: Combining all focused enhanced data sources...')
df_final = combine_all_focused_enhanced_data()

print(f'\n✅ Final focused enhanced dataset created:')
print(f'   📊 Total relationships: {len(df_final)}')
if len(df_final) > 0:
    print(f'   🏢 Applications: {df_final["application"].value_counts().to_dict()}')
    print(f'   🔗 Relationship types: {df_final["relationship"].value_counts().to_dict()}')

# Load to Neo4j
print('\n🚀 Loading to Neo4j...')
load_to_focused_neo4j(df_final)

# ========== STAGE 9: CSV EXPORT AND CONNECTION FIXES ==========

def export_and_fix_connections():
    '''Export current data to CSV and fix connection issues'''
    
    # First, let's export the current final dataset to CSV for analysis
    print('📊 Exporting current dataset to CSV for analysis...')
    df_final.to_csv('current_relationships.csv', index=False)
    print(f'✅ Exported {len(df_final)} relationships to current_relationships.csv')
    
    # Analyze current issues
    print('\n🔍 Analyzing connection issues...')
    
    # Issue 1: Check file-to-folder connections
    file_relationships = df_final[df_final['destination_type'] == 'file']
    print(f'📁 Current file relationships: {len(file_relationships)}')
    
    # Issue 2: Check variable connections to classes/methods
    variable_relationships = df_final[df_final['source_type'] == 'variable']
    isolated_variables = variable_relationships[~variable_relationships['source_node'].str.contains('\\.')]
    print(f'🔗 Variable relationships: {len(variable_relationships)}')
    print(f'⚠️ Isolated variables (not connected to class/method): {len(isolated_variables)}')
    
    return df_final

def fix_file_folder_connections(df):
    '''Fix missing file-to-folder connections'''
    print('\n🔧 Fixing file-to-folder connections...')
    
    fixed_records = []
    
    # Get all files and their paths
    file_records = df[df['destination_type'] == 'file'].copy()
    
    for _, file_record in file_records.iterrows():
        file_name = file_record['destination_node']
        file_path = file_record.get('file_path', '')
        app_name = file_record['application']
        
        if file_path and '/' in file_path:
            # Extract the immediate parent folder
            path_parts = file_path.replace('\\\\', '/').split('/')
            if len(path_parts) > 1:
                parent_folder = path_parts[-2]  # Immediate parent folder
                
                # Create proper folder -> file connection
                fixed_records.append({
                    'source_node': parent_folder,
                    'source_type': 'folder',
                    'destination_node': file_name,
                    'destination_type': 'file',
                    'relationship': 'contains',
                    'file_path': file_path,
                    'application': app_name
                })
    
    print(f'✅ Fixed {len(fixed_records)} file-to-folder connections')
    return fixed_records

def fix_variable_class_connections(df):
    '''Fix variable connections to their containing classes/methods'''
    print('\n🔧 Fixing variable-to-class/method connections...')
    
    fixed_records = []
    
    # Get all variable transformations
    var_transforms = df[df['relationship'] == 'transforms_to'].copy()
    
    for _, var_record in var_transforms.iterrows():
        source_var = var_record['source_node']
        target_var = var_record['destination_node']
        file_path = var_record.get('file_path', '')
        app_name = var_record['application']
        
        # Extract class name from file path
        if file_path:
            file_name = os.path.basename(file_path)
            class_name = file_name.replace('.java', '') if file_name.endswith('.java') else file_name
            
            # If variables don't already have class context, add it
            if '.' not in source_var and '.' not in target_var:
                # Create class -> variable connections
                fixed_records.append({
                    'source_node': class_name,
                    'source_type': 'class',
                    'destination_node': source_var,
                    'destination_type': 'variable',
                    'relationship': 'declares_variable',
                    'file_path': file_path,
                    'application': app_name
                })
                
                fixed_records.append({
                    'source_node': class_name,
                    'source_type': 'class',
                    'destination_node': target_var,
                    'destination_type': 'variable',
                    'relationship': 'declares_variable',
                    'file_path': file_path,
                    'application': app_name
                })
    
    print(f'✅ Fixed {len(fixed_records)} variable-to-class connections')
    return fixed_records

def create_method_variable_connections(df):
    '''Create proper method -> variable connections'''
    print('\n🔧 Creating method-to-variable connections...')
    
    fixed_records = []
    
    # Get all methods
    methods = df[df['destination_type'] == 'method'].copy()
    
    # Get all variables with context
    variables = df[df['source_type'] == 'variable'].copy()
    
    for _, method_record in methods.iterrows():
        method_name = method_record['destination_node']
        class_name = method_record['source_node']
        file_path = method_record.get('file_path', '')
        app_name = method_record['application']
        
        # Find variables that should belong to this method
        method_variables = variables[
            (variables['source_node'].str.contains(f'{class_name}.{method_name}.', na=False)) |
            (variables['destination_node'].str.contains(f'{class_name}.{method_name}.', na=False))
        ]
        
        for _, var_record in method_variables.iterrows():
            # Extract the actual variable name
            var_name = var_record['source_node'].split('.')[-1] if '.' in var_record['source_node'] else var_record['source_node']
            
            # Create method -> variable connection
            fixed_records.append({
                'source_node': method_name,
                'source_type': 'method',
                'destination_node': var_name,
                'destination_type': 'variable',
                'relationship': 'uses',
                'file_path': file_path,
                'application': app_name
            })
    
    print(f'✅ Created {len(fixed_records)} method-to-variable connections')
    return fixed_records

# Execute the fixes
print('🔧 Starting connection fixes...')

# Export current state
current_df = export_and_fix_connections()

# Apply fixes
file_fixes = fix_file_folder_connections(current_df)
variable_fixes = fix_variable_class_connections(current_df)
method_fixes = create_method_variable_connections(current_df)

# Combine all fixes
all_fixes = file_fixes + variable_fixes + method_fixes
df_fixes = pd.DataFrame(all_fixes)

print(f'\n📊 Total fixes generated: {len(df_fixes)}')
if len(df_fixes) > 0:
    print(f'   🔗 Fix types: {df_fixes["relationship"].value_counts().to_dict()}')

# Export fixes to CSV
if len(df_fixes) > 0:
    df_fixes.to_csv('connection_fixes.csv', index=False)
    print('✅ Exported fixes to connection_fixes.csv')

# ========== STAGE 10: APPLY FIXES AND RELOAD TO NEO4J ==========

def combine_with_fixes():
    '''Combine original data with fixes'''
    print('🔄 Combining original data with fixes...')
    
    # Start with original data
    all_records = df_final.to_dict('records')
    
    # Add fixes if they exist
    if 'df_fixes' in globals() and len(df_fixes) > 0:
        all_records.extend(df_fixes.to_dict('records'))
        print(f'✅ Added {len(df_fixes)} fix records')
    
    # Remove duplicates based on key fields
    seen = set()
    unique_records = []
    
    for record in all_records:
        key = (record['source_node'], record['source_type'], 
               record['destination_node'], record['destination_type'], 
               record['relationship'])
        if key not in seen:
            seen.add(key)
            unique_records.append(record)
    
    print(f'📊 Removed {len(all_records) - len(unique_records)} duplicates')
    
    return pd.DataFrame(unique_records)

def load_fixed_data_to_neo4j(df_fixed):
    '''Load the fixed data to Neo4j'''
    if graph is None:
        print('❌ Neo4j connection not available')
        return
    
    try:
        # Clear existing data
        print('🧹 Clearing existing data...')
        graph.query('MATCH (n) DETACH DELETE n')
        
        # Load fixed data
        print('📊 Loading fixed data to Neo4j...')
        
        # Node type mapping
        node_type_mapping = {
            'project': 'Project',
            'application': 'Application', 
            'folder': 'Folder',
            'file': 'File',
            'class': 'Class',
            'method': 'Method',
            'interface': 'Interface',
            'variable': 'Variable',
            'endpoint': 'Endpoint',
            'data': 'Data'
        }
        
        # Relationship mapping
        relationship_mapping = {
            'contains': 'CONTAINS',
            'declares': 'DECLARES',
            'declares_variable': 'DECLARES_VARIABLE',
            'exposes': 'EXPOSES',
            'extends': 'EXTENDS',
            'implements': 'IMPLEMENTS',
            'has_field': 'HAS_FIELD',
            'uses': 'USES',
            'transforms_to': 'TRANSFORMS_TO',
            'data_find': 'DATA_FIND'
        }
        
        # Load in batches for better performance
        batch_size = 100
        total_batches = len(df_fixed) // batch_size + (1 if len(df_fixed) % batch_size > 0 else 0)
        
        for i in tqdm(range(0, len(df_fixed), batch_size), desc='Loading batches', total=total_batches):
            batch = df_fixed.iloc[i:i+batch_size]
            
            for _, row in batch.iterrows():
                try:
                    source_label = node_type_mapping.get(row['source_type'], row['source_type'].title())
                    target_label = node_type_mapping.get(row['destination_type'], row['destination_type'].title())
                    neo4j_rel_type = relationship_mapping.get(row['relationship'], row['relationship'].upper().replace(' ', '_'))
                    
                    # Create nodes and relationship
                    cypher = f"""
                    MERGE (source:{source_label} {{name: $source_name, application: $app}})
                    MERGE (target:{target_label} {{name: $target_name, application: $app}})
                    MERGE (source)-[:{neo4j_rel_type}]->(target)
                    """
                    
                    graph.query(cypher, {
                        'source_name': str(row['source_node']),
                        'target_name': str(row['destination_node']),
                        'app': str(row['application'])
                    })
                
                except Exception as e:
                    continue
        
        # Create indexes
        indexes = [
            'CREATE INDEX IF NOT EXISTS FOR (n:Project) ON (n.name)',
            'CREATE INDEX IF NOT EXISTS FOR (n:Application) ON (n.name)',
            'CREATE INDEX IF NOT EXISTS FOR (n:Class) ON (n.name)',
            'CREATE INDEX IF NOT EXISTS FOR (n:Method) ON (n.name)',
            'CREATE INDEX IF NOT EXISTS FOR (n:File) ON (n.name)',
            'CREATE INDEX IF NOT EXISTS FOR (n:Variable) ON (n.name)',
            'CREATE INDEX IF NOT EXISTS FOR (n:Data) ON (n.name)'
        ]
        
        for index in indexes:
            try:
                graph.query(index)
            except:
                pass
        
        print('✅ Fixed data successfully loaded to Neo4j')
        
        # Get final statistics
        stats = graph.query("""
        MATCH (n)
        RETURN labels(n)[0] as node_type, count(*) as count
        ORDER BY count DESC
        """)
        
        print('\n📊 Final Neo4j Node Statistics:')
        for stat in stats:
            print(f'   {stat["node_type"]}: {stat["count"]} nodes')
        
        rel_stats = graph.query("""
        MATCH ()-[r]->()
        RETURN type(r) as relationship_type, count(*) as count
        ORDER BY count DESC
        """)
        
        print('\n🔗 Final Neo4j Relationship Statistics:')
        for stat in rel_stats:
            print(f'   {stat["relationship_type"]}: {stat["count"]} relationships')
    
    except Exception as e:
        print(f'❌ Error loading fixed data to Neo4j: {e}')

# Apply the fixes
print('\n🔧 Applying all fixes...')
df_final_fixed = combine_with_fixes()

print(f'\n✅ Final fixed dataset:')
print(f'   📊 Total relationships: {len(df_final_fixed)}')
if len(df_final_fixed) > 0:
    print(f'   🔗 Relationship types: {df_final_fixed["relationship"].value_counts().to_dict()}')
    print(f'   🏢 Applications: {df_final_fixed["application"].value_counts().to_dict()}')

# Export final fixed dataset
df_final_fixed.to_csv('final_fixed_relationships.csv', index=False)
print(f'✅ Exported final fixed dataset to final_fixed_relationships.csv')

# Load to Neo4j
print('\n🚀 Loading fixed data to Neo4j...')
load_fixed_data_to_neo4j(df_final_fixed)

print('\n🎉 CONNECTION FIXES COMPLETE!')
print('\n✅ Fixed Issues:')
print('   🔗 File-to-folder connections properly established')
print('   🔗 Variables now connected to their containing classes')
print('   🔗 Method-to-variable relationships created')
print('   🔗 Proper hierarchical structure maintained')
print('\n📁 CSV Files Generated:')
print('   📄 current_relationships.csv - Original data')
print('   📄 connection_fixes.csv - Applied fixes')
print('   📄 final_fixed_relationships.csv - Complete fixed dataset')

df_final_fixed.info()

# ========== STAGE 11: STANDARDIZATION AND PROJECT CONNECTION FIXES ==========

def fix_project_application_connections():
    '''Fix project to have proper connections to both applications'''
    print('🔧 Fixing project-to-application connections...')
    
    # Create proper PROJECT -> APPLICATION connections
    project_connections = [
        {
            'source_node': 'OneInsights',
            'source_type': 'project',
            'destination_node': 'ServiceBolt',
            'destination_type': 'application',
            'relationship': 'contains',
            'file_path': None,
            'application': 'ServiceBolt'
        },
        {
            'source_node': 'OneInsights',
            'source_type': 'project',
            'destination_node': 'UnifiedBolt',
            'destination_type': 'application',
            'relationship': 'contains',
            'file_path': None,
            'application': 'UnifiedBolt'
        }
    ]
    
    print(f'✅ Created {len(project_connections)} project-to-application connections')
    return project_connections

def standardize_variable_names(df):
    '''Standardize variable names throughout the pipeline'''
    print('🔧 Standardizing variable names...')
    
    standardized_records = []
    variable_mapping = {}  # Track variable name mappings
    
    # Process all records to standardize variable names
    for _, record in df.iterrows():
        new_record = record.copy()
        
        # Get context information
        file_path = record.get('file_path', '')
        app_name = record['application']
        
        # Extract class name from file path
        class_name = 'Unknown'
        if file_path:
            file_name = os.path.basename(file_path)
            class_name = file_name.replace('.java', '') if file_name.endswith('.java') else file_name
        
        # Standardize source node if it's a variable
        if record['source_type'] == 'variable':
            source_var = record['source_node']
            
            # Check if already standardized
            if '.' not in source_var:
                # Find the method context if available
                method_name = 'field'  # Default to field level
                
                # Look for method context in the same file
                method_records = df[
                    (df['file_path'] == file_path) & 
                    (df['destination_type'] == 'method')
                ]
                
                if len(method_records) > 0:
                    # Use the first method found (could be enhanced with better logic)
                    method_name = method_records.iloc[0]['destination_node']
                
                # Create standardized name: ClassName.methodName.variableName
                standardized_name = f"{class_name}.{method_name}.{source_var}"
                new_record['source_node'] = standardized_name
                
                # Track the mapping
                variable_mapping[source_var] = standardized_name
        
        # Standardize destination node if it's a variable
        if record['destination_type'] == 'variable':
            dest_var = record['destination_node']
            
            # Check if already standardized
            if '.' not in dest_var:
                # Find the method context if available
                method_name = 'field'  # Default to field level
                
                # Look for method context in the same file
                method_records = df[
                    (df['file_path'] == file_path) & 
                    (df['destination_type'] == 'method')
                ]
                
                if len(method_records) > 0:
                    # Use the first method found (could be enhanced with better logic)
                    method_name = method_records.iloc[0]['destination_node']
                
                # Create standardized name: ClassName.methodName.variableName
                standardized_name = f"{class_name}.{method_name}.{dest_var}"
                new_record['destination_node'] = standardized_name
                
                # Track the mapping
                variable_mapping[dest_var] = standardized_name
        
        standardized_records.append(new_record.to_dict())
    
    print(f'✅ Standardized {len(variable_mapping)} variable names')
    print(f'📊 Sample mappings: {dict(list(variable_mapping.items())[:5])}')
    
    return pd.DataFrame(standardized_records), variable_mapping

def create_proper_variable_connections(df, variable_mapping):
    '''Create proper connections for standardized variables'''
    print('🔧 Creating proper variable connections...')
    
    connection_records = []
    
    # For each standardized variable, create proper connections
    for original_var, standardized_var in variable_mapping.items():
        parts = standardized_var.split('.')
        if len(parts) == 3:
            class_name, method_name, var_name = parts
            
            # Find the file path and application for this class
            class_records = df[df['destination_node'] == class_name]
            if len(class_records) > 0:
                class_record = class_records.iloc[0]
                file_path = class_record.get('file_path', '')
                app_name = class_record['application']
                
                # Create class -> variable connection
                connection_records.append({
                    'source_node': class_name,
                    'source_type': 'class',
                    'destination_node': standardized_var,
                    'destination_type': 'variable',
                    'relationship': 'declares_variable',
                    'file_path': file_path,
                    'application': app_name
                })
                
                # Create method -> variable connection if method is not 'field'
                if method_name != 'field':
                    connection_records.append({
                        'source_node': method_name,
                        'source_type': 'method',
                        'destination_node': standardized_var,
                        'destination_type': 'variable',
                        'relationship': 'uses',
                        'file_path': file_path,
                        'application': app_name
                    })
    
    print(f'✅ Created {len(connection_records)} proper variable connections')
    return connection_records

def apply_standardization_fixes(df):
    '''Apply all standardization fixes'''
    print('\n🔧 Applying standardization fixes...')
    
    # Fix 1: Project-to-application connections
    project_fixes = fix_project_application_connections()
    
    # Fix 2: Standardize variable names
    df_standardized, variable_mapping = standardize_variable_names(df)
    
    # Fix 3: Create proper variable connections
    variable_connections = create_proper_variable_connections(df_standardized, variable_mapping)
    
    # Combine all fixes
    all_fixes = project_fixes + variable_connections
    df_all_fixes = pd.DataFrame(all_fixes)
    
    # Combine with standardized data
    final_records = df_standardized.to_dict('records') + all_fixes
    
    # Remove duplicates
    seen = set()
    unique_records = []
    
    for record in final_records:
        key = (record['source_node'], record['source_type'], 
               record['destination_node'], record['destination_type'], 
               record['relationship'])
        if key not in seen:
            seen.add(key)
            unique_records.append(record)
    
    print(f'📊 Removed {len(final_records) - len(unique_records)} duplicates')
    
    return pd.DataFrame(unique_records)

# Apply standardization fixes
print('🔧 Starting standardization fixes...')

# Apply fixes to the current final dataset
df_standardized_final = apply_standardization_fixes(df_final_fixed)

print(f'\n✅ Standardization complete:')
print(f'   📊 Total relationships: {len(df_standardized_final)}')
if len(df_standardized_final) > 0:
    print(f'   🔗 Relationship types: {df_standardized_final["relationship"].value_counts().to_dict()}')
    print(f'   🏢 Applications: {df_standardized_final["application"].value_counts().to_dict()}')

# Export standardized dataset
df_standardized_final.to_csv('standardized_final_relationships.csv', index=False)
print(f'✅ Exported standardized dataset to standardized_final_relationships.csv')

# ========== STAGE 12: FINAL NEO4J LOAD WITH STANDARDIZED DATA ==========

def load_standardized_data_to_neo4j(df_standardized):
    '''Load the standardized data to Neo4j with proper project connections'''
    if graph is None:
        print('❌ Neo4j connection not available')
        return
    
    try:
        # Clear existing data
        print('🧹 Clearing existing data...')
        graph.query('MATCH (n) DETACH DELETE n')
        
        # Load standardized data
        print('📊 Loading standardized data to Neo4j...')
        
        # Node type mapping
        node_type_mapping = {
            'project': 'Project',
            'application': 'Application', 
            'folder': 'Folder',
            'file': 'File',
            'class': 'Class',
            'method': 'Method',
            'interface': 'Interface',
            'variable': 'Variable',
            'endpoint': 'Endpoint',
            'data': 'Data'
        }
        
        # Relationship mapping
        relationship_mapping = {
            'contains': 'CONTAINS',
            'declares': 'DECLARES',
            'declares_variable': 'DECLARES_VARIABLE',
            'exposes': 'EXPOSES',
            'extends': 'EXTENDS',
            'implements': 'IMPLEMENTS',
            'has_field': 'HAS_FIELD',
            'uses': 'USES',
            'transforms_to': 'TRANSFORMS_TO',
            'data_find': 'DATA_FIND'
        }
        
        # Load in batches for better performance
        batch_size = 100
        total_batches = len(df_standardized) // batch_size + (1 if len(df_standardized) % batch_size > 0 else 0)
        
        for i in tqdm(range(0, len(df_standardized), batch_size), desc='Loading standardized batches', total=total_batches):
            batch = df_standardized.iloc[i:i+batch_size]
            
            for _, row in batch.iterrows():
                try:
                    source_label = node_type_mapping.get(row['source_type'], row['source_type'].title())
                    target_label = node_type_mapping.get(row['destination_type'], row['destination_type'].title())
                    neo4j_rel_type = relationship_mapping.get(row['relationship'], row['relationship'].upper().replace(' ', '_'))
                    
                    # Create nodes and relationship with additional properties for variables
                    if row['source_type'] == 'variable' or row['destination_type'] == 'variable':
                        # For variables, add context information
                        cypher = f"""
                        MERGE (source:{source_label} {{name: $source_name, application: $app}})
                        MERGE (target:{target_label} {{name: $target_name, application: $app}})
                        MERGE (source)-[:{neo4j_rel_type}]->(target)
                        """
                    else:
                        # Standard relationship
                        cypher = f"""
                        MERGE (source:{source_label} {{name: $source_name, application: $app}})
                        MERGE (target:{target_label} {{name: $target_name, application: $app}})
                        MERGE (source)-[:{neo4j_rel_type}]->(target)
                        """
                    
                    graph.query(cypher, {
                        'source_name': str(row['source_node']),
                        'target_name': str(row['destination_node']),
                        'app': str(row['application'])
                    })
                
                except Exception as e:
                    continue
        
        # Create indexes
        indexes = [
            'CREATE INDEX IF NOT EXISTS FOR (n:Project) ON (n.name)',
            'CREATE INDEX IF NOT EXISTS FOR (n:Application) ON (n.name)',
            'CREATE INDEX IF NOT EXISTS FOR (n:Class) ON (n.name)',
            'CREATE INDEX IF NOT EXISTS FOR (n:Method) ON (n.name)',
            'CREATE INDEX IF NOT EXISTS FOR (n:File) ON (n.name)',
            'CREATE INDEX IF NOT EXISTS FOR (n:Variable) ON (n.name)',
            'CREATE INDEX IF NOT EXISTS FOR (n:Data) ON (n.name)'
        ]
        
        for index in indexes:
            try:
                graph.query(index)
            except:
                pass
        
        print('✅ Standardized data successfully loaded to Neo4j')
        
        # Verify project connections
        project_connections = graph.query("""
        MATCH (p:Project)-[:CONTAINS]->(a:Application)
        RETURN p.name as project, collect(a.name) as applications
        """)
        
        print('\n🏗️ Project-Application Connections:')
        for conn in project_connections:
            print(f'   {conn["project"]} -> {conn["applications"]}')
        
        # Verify variable standardization
        variable_samples = graph.query("""
        MATCH (v:Variable)
        WHERE v.name CONTAINS '.'
        RETURN v.name as variable_name
        LIMIT 10
        """)
        
        print('\n🔗 Standardized Variable Samples:')
        for var in variable_samples:
            print(f'   {var["variable_name"]}')
        
        # Get final statistics
        stats = graph.query("""
        MATCH (n)
        RETURN labels(n)[0] as node_type, count(*) as count
        ORDER BY count DESC
        """)
        
        print('\n📊 Final Standardized Neo4j Node Statistics:')
        for stat in stats:
            print(f'   {stat["node_type"]}: {stat["count"]} nodes')
        
        rel_stats = graph.query("""
        MATCH ()-[r]->()
        RETURN type(r) as relationship_type, count(*) as count
        ORDER BY count DESC
        """)
        
        print('\n🔗 Final Standardized Neo4j Relationship Statistics:')
        for stat in rel_stats:
            print(f'   {stat["relationship_type"]}: {stat["count"]} relationships')
    
    except Exception as e:
        print(f'❌ Error loading standardized data to Neo4j: {e}')

# Load standardized data to Neo4j
print('\n🚀 Loading standardized data to Neo4j...')
load_standardized_data_to_neo4j(df_standardized_final)

print('\n🎉 STANDARDIZATION COMPLETE!')
print('\n✅ Fixed Issues:')
print('   🏗️ OneInsights project now connects to BOTH applications')
print('   🔗 Variable names standardized as ClassName.methodName.variableName')
print('   🔗 Proper variable-to-class-to-method connections established')
print('   🔗 Complete traceability throughout the pipeline')
print('\n📁 Final CSV File:')
print('   📄 standardized_final_relationships.csv - Complete standardized dataset')
print('\n🎯 Now you can track variables like:')
print('   📊 UserController.getUserById.userService')
print('   📊 DataService.processData.resultList')
print('   📊 AuthService.validateToken.tokenData')

# ========== FIX FOR TYPEERROR: Handle NaN file_path values ==========

def standardize_variable_names_fixed(df):
    '''Standardize variable names throughout the pipeline - FIXED VERSION'''
    print('🔧 Standardizing variable names (fixed version)...')
    
    standardized_records = []
    variable_mapping = {}  # Track variable name mappings
    
    # Process all records to standardize variable names
    for _, record in df.iterrows():
        new_record = record.copy()
        
        # Get context information - FIX: Handle NaN values properly
        file_path = record.get('file_path', '')
        app_name = record['application']
        
        # Extract class name from file path - FIX: Handle NaN and non-string values
        class_name = 'Unknown'
        if file_path and pd.notna(file_path) and str(file_path).strip() != '' and str(file_path) != 'nan':
            try:
                file_name = os.path.basename(str(file_path))
                class_name = file_name.replace('.java', '') if file_name.endswith('.java') else file_name
            except Exception as e:
                print(f'⚠️ Error processing file_path {file_path}: {e}')
                class_name = 'Unknown'
        
        # Standardize source node if it's a variable
        if record['source_type'] == 'variable':
            source_var = record['source_node']
            
            # Check if already standardized
            if '.' not in str(source_var):
                # Find the method context if available
                method_name = 'field'  # Default to field level
                
                # Look for method context in the same file
                if file_path and pd.notna(file_path):
                    method_records = df[
                        (df['file_path'] == file_path) & 
                        (df['destination_type'] == 'method')
                    ]
                    
                    if len(method_records) > 0:
                        # Use the first method found
                        method_name = method_records.iloc[0]['destination_node']
                
                # Create standardized name: ClassName.methodName.variableName
                standardized_name = f"{class_name}.{method_name}.{source_var}"
                new_record['source_node'] = standardized_name
                
                # Track the mapping
                variable_mapping[source_var] = standardized_name
        
        # Standardize destination node if it's a variable
        if record['destination_type'] == 'variable':
            dest_var = record['destination_node']
            
            # Check if already standardized
            if '.' not in str(dest_var):
                # Find the method context if available
                method_name = 'field'  # Default to field level
                
                # Look for method context in the same file
                if file_path and pd.notna(file_path):
                    method_records = df[
                        (df['file_path'] == file_path) & 
                        (df['destination_type'] == 'method')
                    ]
                    
                    if len(method_records) > 0:
                        # Use the first method found
                        method_name = method_records.iloc[0]['destination_node']
                
                # Create standardized name: ClassName.methodName.variableName
                standardized_name = f"{class_name}.{method_name}.{dest_var}"
                new_record['destination_node'] = standardized_name
                
                # Track the mapping
                variable_mapping[dest_var] = standardized_name
        
        standardized_records.append(new_record.to_dict())
    
    print(f'✅ Standardized {len(variable_mapping)} variable names')
    print(f'📊 Sample mappings: {dict(list(variable_mapping.items())[:5])}')
    
    return pd.DataFrame(standardized_records), variable_mapping

def apply_standardization_fixes_fixed(df):
    '''Apply all standardization fixes - FIXED VERSION'''
    print('\n🔧 Applying standardization fixes (fixed version)...')
    
    # Fix 1: Project-to-application connections
    project_fixes = [
        {
            'source_node': 'OneInsights',
            'source_type': 'project',
            'destination_node': 'ServiceBolt',
            'destination_type': 'application',
            'relationship': 'contains',
            'file_path': None,
            'application': 'ServiceBolt'
        },
        {
            'source_node': 'OneInsights',
            'source_type': 'project',
            'destination_node': 'UnifiedBolt',
            'destination_type': 'application',
            'relationship': 'contains',
            'file_path': None,
            'application': 'UnifiedBolt'
        }
    ]
    
    # Fix 2: Standardize variable names (FIXED)
    df_standardized, variable_mapping = standardize_variable_names_fixed(df)
    
    # Fix 3: Create proper variable connections
    variable_connections = []
    for original_var, standardized_var in variable_mapping.items():
        parts = standardized_var.split('.')
        if len(parts) == 3:
            class_name, method_name, var_name = parts
            
            # Find the file path and application for this class
            class_records = df[df['destination_node'] == class_name]
            if len(class_records) > 0:
                class_record = class_records.iloc[0]
                file_path = class_record.get('file_path', '')
                app_name = class_record['application']
                
                # Create class -> variable connection
                variable_connections.append({
                    'source_node': class_name,
                    'source_type': 'class',
                    'destination_node': standardized_var,
                    'destination_type': 'variable',
                    'relationship': 'declares_variable',
                    'file_path': file_path,
                    'application': app_name
                })
                
                # Create method -> variable connection if method is not 'field'
                if method_name != 'field':
                    variable_connections.append({
                        'source_node': method_name,
                        'source_type': 'method',
                        'destination_node': standardized_var,
                        'destination_type': 'variable',
                        'relationship': 'uses',
                        'file_path': file_path,
                        'application': app_name
                    })
    
    # Combine all fixes
    all_fixes = project_fixes + variable_connections
    
    # Combine with standardized data
    final_records = df_standardized.to_dict('records') + all_fixes
    
    # Remove duplicates
    seen = set()
    unique_records = []
    
    for record in final_records:
        key = (record['source_node'], record['source_type'], 
               record['destination_node'], record['destination_type'], 
               record['relationship'])
        if key not in seen:
            seen.add(key)
            unique_records.append(record)
    
    print(f'📊 Removed {len(final_records) - len(unique_records)} duplicates')
    
    return pd.DataFrame(unique_records)

# Apply the FIXED standardization
print('🔧 Applying FIXED standardization...')
try:
    df_standardized_final = apply_standardization_fixes_fixed(df_final_fixed)
    
    print(f'\n✅ Standardization complete:')
    print(f'   📊 Total relationships: {len(df_standardized_final)}')
    if len(df_standardized_final) > 0:
        print(f'   🔗 Relationship types: {df_standardized_final["relationship"].value_counts().to_dict()}')
        print(f'   🏢 Applications: {df_standardized_final["application"].value_counts().to_dict()}')
    
    # Export standardized dataset
    df_standardized_final.to_csv('standardized_final_relationships_fixed.csv', index=False)
    print(f'✅ Exported standardized dataset to standardized_final_relationships_fixed.csv')
    
except Exception as e:
    print(f'❌ Error in standardization: {e}')
    print('📊 Checking data types in df_final_fixed...')
    if 'df_final_fixed' in globals():
        print(f'   Shape: {df_final_fixed.shape}')
        print(f'   Columns: {df_final_fixed.columns.tolist()}')
        print(f'   file_path column info:')
        print(f'     - Type: {df_final_fixed["file_path"].dtype}')
        print(f'     - Null count: {df_final_fixed["file_path"].isnull().sum()}')
        print(f'     - Sample values: {df_final_fixed["file_path"].head().tolist()}')
    else:
        print('   df_final_fixed not found in globals')