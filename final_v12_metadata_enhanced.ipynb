{
 "cells": [
  {
   "cell_type": "markdown",
   "id": "header",
   "metadata": {},
   "source": [
    "# 🚀 OneInsights Java Code Lineage Analysis v12 - Metadata Enhanced\n",
    "\n",
    "## 🎯 Objectives:\n",
    "1. **Complete Hierarchy**: PROJECT → APPLICATION → FOLDER → FILE → CLASS → METHOD → VARIABLE\n",
    "2. **Standardized Variables**: `ClassName.methodName.variableName` format (global only)\n",
    "3. **Rich Metadata**: Comprehensive metadata for all node types\n",
    "4. **Enhanced LLM Context**: Metadata-driven LLM processing\n",
    "5. **Project Structure**: OneInsights connects to BOTH applications\n",
    "\n",
    "## 📊 **Metadata Schema:**\n",
    "- **Class**: parent_class, child_classes, interfaces, annotations, package\n",
    "- **Method**: return_type, parameters, annotations, access_modifier, is_static\n",
    "- **Variable**: definition_location, storage_type, transformations, scope, data_type\n",
    "- **File**: package_name, imports, class_count, method_count\n",
    "- **Folder**: file_count, class_count, package_structure\n",
    "- **Application**: technology_stack, dependencies, api_endpoints\n",
    "\n",
    "## 📋 Processing Stages:\n",
    "- **Stage 1**: Setup and Configuration\n",
    "- **Stage 2**: File Discovery and Hierarchy Building with Metadata\n",
    "- **Stage 3**: AST-based Code Analysis with Rich Metadata\n",
    "- **Stage 4**: Global Variable Tracking with Transformation Metadata\n",
    "- **Stage 4B**: Metadata-Enhanced LLM Preparation\n",
    "- **Stage 4C**: Metadata-Driven LLM Processing\n",
    "- **Stage 5**: Data Cleaning and Metadata Validation\n",
    "- **Stage 6**: Neo4j Loading with Metadata Properties"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "stage1_setup",
   "metadata": {},
   "outputs": [],
   "source": [
    "# ========== STAGE 1: SETUP AND CONFIGURATION ==========\n",
    "\n",
    "import os\n",
    "import pandas as pd\n",
    "import re\n",
    "import json\n",
    "from pathlib import Path\n",
    "from tqdm import tqdm\n",
    "from collections import defaultdict\n",
    "import warnings\n",
    "warnings.filterwarnings('ignore')\n",
    "\n",
    "# Tree-sitter for AST parsing\n",
    "from tree_sitter import Language, Parser\n",
    "import tree_sitter_java as tsjava\n",
    "\n",
    "# LangChain for LLM processing\n",
    "from langchain_community.document_loaders import TextLoader\n",
    "from langchain.text_splitter import RecursiveCharacterTextSplitter\n",
    "from langchain_google_genai import ChatGoogleGenerativeAI\n",
    "from langchain_experimental.graph_transformers import LLMGraphTransformer\n",
    "from langchain_community.graphs import Neo4jGraph\n",
    "\n",
    "# ========== CONFIGURATION ==========\n",
    "BASE_PATH = Path(r'C:/Shaik/sample/OneInsights')\n",
    "NEO4J_URI = 'bolt://localhost:7687'\n",
    "NEO4J_USER = 'neo4j'\n",
    "NEO4J_PASSWORD = 'Test@7889'\n",
    "NEO4J_DB = 'oneinsights-v12'\n",
    "GOOGLE_API_KEY = 'AIzaSyD8qIjUPsRbhJr59qoIIhczbPPKA2hter0'\n",
    "\n",
    "# ========== INITIALIZE COMPONENTS ==========\n",
    "print('🔧 Initializing components...')\n",
    "\n",
    "# Neo4j connection\n",
    "try:\n",
    "    graph = Neo4jGraph(url=NEO4J_URI, username=NEO4J_USER, password=NEO4J_PASSWORD, database=NEO4J_DB)\n",
    "    print('✅ Neo4j connection established')\n",
    "except Exception as e:\n",
    "    print(f'❌ Neo4j connection failed: {e}')\n",
    "    graph = None\n",
    "\n",
    "# Tree-sitter Java parser\n",
    "JAVA_LANGUAGE = Language(tsjava.language())\n",
    "parser = Parser(JAVA_LANGUAGE)\n",
    "\n",
    "# LLM for enhanced extraction\n",
    "llm = ChatGoogleGenerativeAI(\n",
    "    model='gemini-2.0-flash-exp',\n",
    "    temperature=0,\n",
    "    google_api_key=GOOGLE_API_KEY\n",
    ")\n",
    "\n",
    "# ========== APPLICATION MAPPING ==========\n",
    "APPLICATIONS = {\n",
    "    'ServiceBolt': 'REST API Service Layer',\n",
    "    'UnifiedBolt': 'Core Business Logic and Data Layer'\n",
    "}\n",
    "\n",
    "# ========== NOISE FILTERING (Global Variables Only) ==========\n",
    "NOISE_VARIABLES = {\n",
    "    'i', 'j', 'k', 'l', 'm', 'n', 'x', 'y', 'z',\n",
    "    'temp', 'tmp', 'obj', 'item', 'elem', 'node', 'val', 'value',\n",
    "    'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w',\n",
    "    'this', 'super', 'null', 'true', 'false', 'void', 'return',\n",
    "    'it', 'ex', 'e1', 'e2', 'o1', 'o2'\n",
    "}\n",
    "\n",
    "def is_meaningful_variable(var_name):\n",
    "    '''Filter meaningful variables only (global scope focus)'''\n",
    "    if not var_name or len(var_name) < 3:\n",
    "        return False\n",
    "    if var_name.lower() in NOISE_VARIABLES:\n",
    "        return False\n",
    "    if re.match(r'^[a-zA-Z][a-zA-Z0-9_]*$', var_name):\n",
    "        return True\n",
    "    return False\n",
    "\n",
    "def is_global_variable(var_name, context):\n",
    "    '''Check if variable is global (class-level field or service injection)'''\n",
    "    # Global indicators\n",
    "    global_patterns = [\n",
    "        r'@Autowired',\n",
    "        r'@Inject',\n",
    "        r'@Resource',\n",
    "        r'private\\s+\\w+\\s+' + re.escape(var_name),\n",
    "        r'protected\\s+\\w+\\s+' + re.escape(var_name),\n",
    "        r'public\\s+\\w+\\s+' + re.escape(var_name),\n",
    "        r'static\\s+\\w+\\s+' + re.escape(var_name)\n",
    "    ]\n",
    "    \n",
    "    for pattern in global_patterns:\n",
    "        if re.search(pattern, context, re.IGNORECASE):\n",
    "            return True\n",
    "    \n",
    "    return False\n",
    "\n",
    "# ========== METADATA STRUCTURES ==========\n",
    "class MetadataCollector:\n",
    "    def __init__(self):\n",
    "        self.class_metadata = {}     # Class metadata with inheritance\n",
    "        self.method_metadata = {}    # Method metadata with signatures\n",
    "        self.variable_metadata = {}  # Global variable metadata with transformations\n",
    "        self.file_metadata = {}      # File metadata with imports/packages\n",
    "        self.folder_metadata = {}    # Folder metadata with structure\n",
    "        self.app_metadata = {}       # Application metadata with tech stack\n",
    "    \n",
    "    def add_class_metadata(self, class_name, file_path, app_name, **kwargs):\n",
    "        '''Add comprehensive class metadata'''\n",
    "        self.class_metadata[class_name] = {\n",
    "            'file_path': file_path,\n",
    "            'application': app_name,\n",
    "            'parent_class': kwargs.get('parent_class'),\n",
    "            'child_classes': kwargs.get('child_classes', []),\n",
    "            'interfaces': kwargs.get('interfaces', []),\n",
    "            'annotations': kwargs.get('annotations', []),\n",
    "            'package': kwargs.get('package'),\n",
    "            'access_modifier': kwargs.get('access_modifier', 'public'),\n",
    "            'is_abstract': kwargs.get('is_abstract', False),\n",
    "            'is_final': kwargs.get('is_final', False),\n",
    "            'field_count': kwargs.get('field_count', 0),\n",
    "            'method_count': kwargs.get('method_count', 0)\n",
    "        }\n",
    "    \n",
    "    def add_method_metadata(self, method_key, class_name, file_path, app_name, **kwargs):\n",
    "        '''Add comprehensive method metadata'''\n",
    "        self.method_metadata[method_key] = {\n",
    "            'class': class_name,\n",
    "            'file_path': file_path,\n",
    "            'application': app_name,\n",
    "            'return_type': kwargs.get('return_type'),\n",
    "            'parameters': kwargs.get('parameters', []),\n",
    "            'annotations': kwargs.get('annotations', []),\n",
    "            'access_modifier': kwargs.get('access_modifier', 'public'),\n",
    "            'is_static': kwargs.get('is_static', False),\n",
    "            'is_abstract': kwargs.get('is_abstract', False),\n",
    "            'is_final': kwargs.get('is_final', False),\n",
    "            'throws_exceptions': kwargs.get('throws_exceptions', []),\n",
    "            'variable_count': kwargs.get('variable_count', 0)\n",
    "        }\n",
    "    \n",
    "    def add_variable_metadata(self, var_key, class_name, method_name, file_path, app_name, **kwargs):\n",
    "        '''Add comprehensive global variable metadata'''\n",
    "        self.variable_metadata[var_key] = {\n",
    "            'original_name': kwargs.get('original_name'),\n",
    "            'class': class_name,\n",
    "            'method': method_name,\n",
    "            'file_path': file_path,\n",
    "            'application': app_name,\n",
    "            'definition_location': kwargs.get('definition_location'),\n",
    "            'storage_type': kwargs.get('storage_type', 'field'),  # field, parameter, local\n",
    "            'data_type': kwargs.get('data_type'),\n",
    "            'access_modifier': kwargs.get('access_modifier'),\n",
    "            'is_static': kwargs.get('is_static', False),\n",
    "            'is_final': kwargs.get('is_final', False),\n",
    "            'annotations': kwargs.get('annotations', []),\n",
    "            'transformations': kwargs.get('transformations', []),\n",
    "            'scope': kwargs.get('scope', 'global'),\n",
    "            'usage_count': kwargs.get('usage_count', 0)\n",
    "        }\n",
    "\n",
    "# Initialize metadata collector\n",
    "metadata = MetadataCollector()\n",
    "\n",
    "# ========== GLOBAL DATA STRUCTURES ==========\n",
    "all_relationships = []  # Master list of all relationships\n",
    "class_registry = {}     # Track all classes and their details\n",
    "method_registry = {}    # Track all methods and their details\n",
    "variable_registry = {}  # Track all global variables and their context\n",
    "\n",
    "print('🚀 Setup complete! Ready for metadata-enhanced v12 analysis...')"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "stage2_hierarchy_metadata",
   "metadata": {},
   "outputs": [],
   "source": [
    "# ========== STAGE 2: FILE DISCOVERY AND HIERARCHY BUILDING WITH METADATA ==========\n",
    "\n",
    "def extract_package_from_file(file_path):\n",
    "    '''Extract package name from Java file'''\n",
    "    try:\n",
    "        with open(file_path, 'r', encoding='utf-8') as f:\n",
    "            content = f.read()\n",
    "            package_match = re.search(r'package\\s+([\\w\\.]+);', content)\n",
    "            if package_match:\n",
    "                return package_match.group(1)\n",
    "    except Exception:\n",
    "        pass\n",
    "    return None\n",
    "\n",
    "def extract_imports_from_file(file_path):\n",
    "    '''Extract import statements from Java file'''\n",
    "    imports = []\n",
    "    try:\n",
    "        with open(file_path, 'r', encoding='utf-8') as f:\n",
    "            content = f.read()\n",
    "            import_matches = re.findall(r'import\\s+([\\w\\.\\*]+);', content)\n",
    "            imports = import_matches\n",
    "    except Exception:\n",
    "        pass\n",
    "    return imports\n",
    "\n",
    "def extract_class_count_from_file(file_path):\n",
    "    '''Extract number of classes in a Java file'''\n",
    "    try:\n",
    "        with open(file_path, 'r', encoding='utf-8') as f:\n",
    "            content = f.read()\n",
    "            class_matches = re.findall(r'(public|private|protected)?\\s*(abstract|final)?\\s*class\\s+([\\w<>]+)', content)\n",
    "            return len(class_matches)\n",
    "    except Exception:\n",
    "        pass\n",
    "    return 0\n",
    "\n",
    "def build_project_hierarchy_with_metadata():\n",
    "    '''Build complete project hierarchy with rich metadata'''\n",
    "    print('🏗️ Building project hierarchy with metadata...')\n",
    "    \n",
    "    hierarchy_relationships = []\n",
    "    \n",
    "    # PROJECT level with metadata\n",
    "    project_name = 'OneInsights'\n",
    "    project_metadata = {\n",
    "        'description': 'Unified insights platform for data analysis',\n",
    "        'applications': list(APPLICATIONS.keys()),\n",
    "        'version': '1.0.0'\n",
    "    }\n",
    "    \n",
    "    # APPLICATION level - Connect project to BOTH applications with metadata\n",
    "    for app_name, app_desc in APPLICATIONS.items():\n",
    "        # Add application metadata\n",
    "        app_tech_stack = {\n",
    "            'ServiceBolt': ['Spring Boot', 'REST API', 'Spring Security', 'Spring Data'],\n",
    "            'UnifiedBolt': ['Core Java', 'JPA/Hibernate', 'Business Logic']\n",
    "        }\n",
    "        \n",
    "        metadata.app_metadata[app_name] = {\n",
    "            'description': app_desc,\n",
    "            'technology_stack': app_tech_stack.get(app_name, []),\n",
    "            'dependencies': [],\n",
    "            'api_endpoints': []\n",
    "        }\n",
    "        \n",
    "        # Create relationship with metadata\n",
    "        hierarchy_relationships.append({\n",
    "            'source_node': project_name,\n",
    "            'source_type': 'project',\n",
    "            'destination_node': app_name,\n",
    "            'destination_type': 'application',\n",
    "            'relationship': 'contains',\n",
    "            'file_path': None,\n",
    "            'application': app_name,\n",
    "            'metadata': json.dumps({\n",
    "                'project_metadata': project_metadata,\n",
    "                'application_metadata': metadata.app_metadata[app_name]\n",
    "            })\n",
    "        })\n",
    "    \n",
    "    # FOLDER and FILE level with metadata\n",
    "    for app_name in APPLICATIONS.keys():\n",
    "        app_path = BASE_PATH / app_name\n",
    "        if not app_path.exists():\n",
    "            print(f'⚠️ Application path not found: {app_path}')\n",
    "            continue\n",
    "        \n",
    "        print(f'📁 Processing application: {app_name}')\n",
    "        \n",
    "        # Find all Java files\n",
    "        java_files = list(app_path.rglob('*.java'))\n",
    "        print(f'   Found {len(java_files)} Java files')\n",
    "        \n",
    "        folders_processed = set()\n",
    "        folder_file_counts = defaultdict(int)\n",
    "        folder_class_counts = defaultdict(int)\n",
    "        \n",
    "        # First pass: collect folder statistics\n",
    "        for java_file in java_files:\n",
    "            relative_path = java_file.relative_to(app_path)\n",
    "            folder = str(relative_path.parent)\n",
    "            folder_file_counts[folder] += 1\n",
    "            folder_class_counts[folder] += extract_class_count_from_file(java_file)\n",
    "        \n",
    "        # Second pass: build hierarchy with metadata\n",
    "        for java_file in java_files:\n",
    "            relative_path = java_file.relative_to(app_path)\n",
    "            path_parts = relative_path.parts\n",
    "            \n",
    "            # Extract file metadata\n",
    "            package_name = extract_package_from_file(java_file)\n",
    "            imports = extract_imports_from_file(java_file)\n",
    "            class_count = extract_class_count_from_file(java_file)\n",
    "            \n",
    "            file_metadata = {\n",
    "                'package_name': package_name,\n",
    "                'imports': imports,\n",
    "                'class_count': class_count,\n",
    "                'file_size': os.path.getsize(java_file),\n",
    "                'last_modified': os.path.getmtime(java_file)\n",
    "            }\n",
    "            \n",
    "            metadata.file_metadata[str(java_file)] = file_metadata\n",
    "            \n",
    "            # Build folder hierarchy\n",
    "            current_parent = app_name\n",
    "            current_parent_type = 'application'\n",
    "            \n",
    "            # Process each folder in the path\n",
    "            for i, folder in enumerate(path_parts[:-1]):  # Exclude the file itself\n",
    "                folder_key = '/'.join(path_parts[:i+1])\n",
    "                folder_path = str(app_path / '/'.join(path_parts[:i+1]))\n",
    "                \n",
    "                if folder_key not in folders_processed:\n",
    "                    # Create folder metadata\n",
    "                    folder_metadata = {\n",
    "                        'file_count': folder_file_counts.get(folder_key, 0),\n",
    "                        'class_count': folder_class_counts.get(folder_key, 0),\n",
    "                        'package_structure': package_name.split('.')[:(i+1)] if package_name else [],\n",
    "                        'depth': i + 1\n",
    "                    }\n",
    "                    \n",
    "                    metadata.folder_metadata[folder_key] = folder_metadata\n",
    "                    \n",
    "                    # APPLICATION/FOLDER → FOLDER relationship with metadata\n",
    "                    hierarchy_relationships.append({\n",
    "                        'source_node': current_parent,\n",
    "                        'source_type': current_parent_type,\n",
    "                        'destination_node': folder,\n",
    "                        'destination_type': 'folder',\n",
    "                        'relationship': 'contains',\n",
    "                        'file_path': folder_path,\n",
    "                        'application': app_name,\n",
    "                        'metadata': json.dumps(folder_metadata)\n",
    "                    })\n",
    "                    folders_processed.add(folder_key)\n",
    "                \n",
    "                current_parent = folder\n",
    "                current_parent_type = 'folder'\n",
    "            \n",
    "            # FOLDER → FILE relationship with metadata\n",
    "            file_name = java_file.stem  # Without .java extension\n",
    "            hierarchy_relationships.append({\n",
    "                'source_node': current_parent,\n",
    "                'source_type': current_parent_type,\n",
    "                'destination_node': file_name,\n",
    "                'destination_type': 'file',\n",
    "                'relationship': 'contains',\n",
    "                'file_path': str(java_file),\n",
    "                'application': app_name,\n",
    "                'metadata': json.dumps(file_metadata)\n",
    "            })\n",
    "    \n",
    "    print(f'✅ Built hierarchy with {len(hierarchy_relationships)} relationships and rich metadata')\n",
    "    return hierarchy_relationships\n",
    "\n",
    "# Build the hierarchy with metadata\n",
    "hierarchy_rels = build_project_hierarchy_with_metadata()\n",
    "all_relationships.extend(hierarchy_rels)\n",
    "\n",
    "print(f'📊 Current total relationships: {len(all_relationships)}')"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "stage3_ast_metadata",
   "metadata": {},
   "outputs": [],
   "source": [
    "# ========== STAGE 3: AST-BASED CODE ANALYSIS WITH RICH METADATA ==========\n",
    "\n",
    "def extract_class_info_with_metadata(node, source_code):\n",
    "    '''Extract comprehensive class information with metadata'''\n",
    "    class_name = None\n",
    "    extends_class = None\n",
    "    interfaces = []\n",
    "    annotations = []\n",
    "    access_modifier = 'public'\n",
    "    is_abstract = False\n",
    "    is_final = False\n",
    "    \n",
    "    # Extract class modifiers and annotations\n",
    "    for child in node.children:\n",
    "        if child.type == 'identifier':\n",
    "            class_name = source_code[child.start_byte:child.end_byte]\n",
    "        elif child.type == 'superclass':\n",
    "            for subchild in child.children:\n",
    "                if subchild.type == 'type_identifier':\n",
    "                    extends_class = source_code[subchild.start_byte:subchild.end_byte]\n",
    "        elif child.type == 'super_interfaces':\n",
    "            for subchild in child.children:\n",
    "                if subchild.type == 'type_identifier':\n",
    "                    interfaces.append(source_code[subchild.start_byte:subchild.end_byte])\n",
    "        elif child.type == 'modifiers':\n",
    "            modifiers_text = source_code[child.start_byte:child.end_byte]\n",
    "            if 'private' in modifiers_text:\n",
    "                access_modifier = 'private'\n",
    "            elif 'protected' in modifiers_text:\n",
    "                access_modifier = 'protected'\n",
    "            is_abstract = 'abstract' in modifiers_text\n",
    "            is_final = 'final' in modifiers_text\n",
    "    \n",
    "    # Extract annotations (look for preceding annotation nodes)\n",
    "    parent = node.parent\n",
    "    if parent:\n",
    "        for sibling in parent.children:\n",
    "            if sibling.type == 'annotation' and sibling.start_byte < node.start_byte:\n",
    "                annotation_text = source_code[sibling.start_byte:sibling.end_byte]\n",
    "                annotations.append(annotation_text)\n",
    "    \n",
    "    return {\n",
    "        'class_name': class_name,\n",
    "        'parent_class': extends_class,\n",
    "        'interfaces': interfaces,\n",
    "        'annotations': annotations,\n",
    "        'access_modifier': access_modifier,\n",
    "        'is_abstract': is_abstract,\n",
    "        'is_final': is_final\n",
    "    }\n",
    "\n",
    "def extract_method_info_with_metadata(node, source_code):\n",
    "    '''Extract comprehensive method information with metadata'''\n",
    "    method_name = None\n",
    "    parameters = []\n",
    "    return_type = None\n",
    "    annotations = []\n",
    "    access_modifier = 'public'\n",
    "    is_static = False\n",
    "    is_abstract = False\n",
    "    is_final = False\n",
    "    throws_exceptions = []\n",
    "    \n",
    "    for child in node.children:\n",
    "        if child.type == 'identifier':\n",
    "            method_name = source_code[child.start_byte:child.end_byte]\n",
    "        elif child.type == 'type_identifier' or child.type == 'generic_type':\n",
    "            return_type = source_code[child.start_byte:child.end_byte]\n",
    "        elif child.type == 'formal_parameters':\n",
    "            for param_child in child.children:\n",
    "                if param_child.type == 'formal_parameter':\n",
    "                    param_info = {'name': None, 'type': None}\n",
    "                    for param_part in param_child.children:\n",
    "                        if param_part.type == 'variable_declarator':\n",
    "                            for var_part in param_part.children:\n",
    "                                if var_part.type == 'identifier':\n",
    "                                    param_info['name'] = source_code[var_part.start_byte:var_part.end_byte]\n",
    "                        elif param_part.type in ['type_identifier', 'generic_type']:\n",
    "                            param_info['type'] = source_code[param_part.start_byte:param_part.end_byte]\n",
    "                    if param_info['name'] and is_meaningful_variable(param_info['name']):\n",
    "                        parameters.append(param_info)\n",
    "        elif child.type == 'modifiers':\n",
    "            modifiers_text = source_code[child.start_byte:child.end_byte]\n",
    "            if 'private' in modifiers_text:\n",
    "                access_modifier = 'private'\n",
    "            elif 'protected' in modifiers_text:\n",
    "                access_modifier = 'protected'\n",
    "            is_static = 'static' in modifiers_text\n",
    "            is_abstract = 'abstract' in modifiers_text\n",
    "            is_final = 'final' in modifiers_text\n",
    "        elif child.type == 'throws':\n",
    "            for exception_child in child.children:\n",
    "                if exception_child.type == 'type_identifier':\n",
    "                    throws_exceptions.append(source_code[exception_child.start_byte:exception_child.end_byte])\n",
    "    \n",
    "    # Extract annotations\n",
    "    parent = node.parent\n",
    "    if parent:\n",
    "        for sibling in parent.children:\n",
    "            if sibling.type == 'annotation' and sibling.start_byte < node.start_byte:\n",
    "                annotation_text = source_code[sibling.start_byte:sibling.end_byte]\n",
    "                annotations.append(annotation_text)\n",
    "    \n",
    "    return {\n",
    "        'method_name': method_name,\n",
    "        'return_type': return_type,\n",
    "        'parameters': parameters,\n",
    "        'annotations': annotations,\n",
    "        'access_modifier': access_modifier,\n",
    "        'is_static': is_static,\n",
    "        'is_abstract': is_abstract,\n",
    "        'is_final': is_final,\n",
    "        'throws_exceptions': throws_exceptions\n",
    "    }\n",
    "\n",
    "def extract_global_variables_with_metadata(node, source_code, class_name, method_context=None):\n",
    "    '''Extract global variables with comprehensive metadata'''\n",
    "    variables = []\n",
    "    \n",
    "    def traverse_for_fields(n, is_class_level=False):\n",
    "        '''Traverse to find field declarations (global variables)'''\n",
    "        if n.type == 'field_declaration':\n",
    "            # This is a class-level field (global variable)\n",
    "            field_info = extract_field_metadata(n, source_code)\n",
    "            if field_info and field_info['name']:\n",
    "                variables.append(field_info)\n",
    "        \n",
    "        # Only traverse class body for fields, not method bodies\n",
    "        if n.type in ['class_body', 'class_declaration']:\n",
    "            for child in n.children:\n",
    "                traverse_for_fields(child, True)\n",
    "    \n",
    "    traverse_for_fields(node)\n",
    "    return variables\n",
    "\n",
    "def extract_field_metadata(node, source_code):\n",
    "    '''Extract field metadata from field_declaration node'''\n",
    "    field_name = None\n",
    "    data_type = None\n",
    "    access_modifier = 'private'  # Default for fields\n",
    "    is_static = False\n",
    "    is_final = False\n",
    "    annotations = []\n",
    "    \n",
    "    for child in node.children:\n",
    "        if child.type == 'variable_declarator':\n",
    "            for var_child in child.children:\n",
    "                if var_child.type == 'identifier':\n",
    "                    field_name = source_code[var_child.start_byte:var_child.end_byte]\n",
    "        elif child.type in ['type_identifier', 'generic_type']:\n",
    "            data_type = source_code[child.start_byte:child.end_byte]\n",
    "        elif child.type == 'modifiers':\n",
    "            modifiers_text = source_code[child.start_byte:child.end_byte]\n",
    "            if 'public' in modifiers_text:\n",
    "                access_modifier = 'public'\n",
    "            elif 'protected' in modifiers_text:\n",
    "                access_modifier = 'protected'\n",
    "            is_static = 'static' in modifiers_text\n",
    "            is_final = 'final' in modifiers_text\n",
    "    \n",
    "    # Extract annotations\n",
    "    parent = node.parent\n",
    "    if parent:\n",
    "        for sibling in parent.children:\n",
    "            if sibling.type == 'annotation' and sibling.start_byte < node.start_byte:\n",
    "                annotation_text = source_code[sibling.start_byte:sibling.end_byte]\n",
    "                annotations.append(annotation_text)\n",
    "    \n",
    "    return {\n",
    "        'name': field_name,\n",
    "        'data_type': data_type,\n",
    "        'access_modifier': access_modifier,\n",
    "        'is_static': is_static,\n",
    "        'is_final': is_final,\n",
    "        'annotations': annotations,\n",
    "        'storage_type': 'field'\n",
    "    }\n",
    "\n",
    "print('🔍 Starting AST analysis with rich metadata...')\n",
    "ast_relationships = []\n",
    "\n",
    "for app_name in APPLICATIONS.keys():\n",
    "    app_path = BASE_PATH / app_name\n",
    "    if app_path.exists():\n",
    "        java_files = list(app_path.rglob('*.java'))\n",
    "        print(f'📁 Processing {len(java_files)} files in {app_name}...')\n",
    "        \n",
    "        for java_file in tqdm(java_files, desc=f'AST Analysis - {app_name}'):\n",
    "            try:\n",
    "                with open(java_file, 'r', encoding='utf-8') as f:\n",
    "                    source_code = f.read()\n",
    "            except Exception as e:\n",
    "                print(f'❌ Error reading {java_file}: {e}')\n",
    "                continue\n",
    "            \n",
    "            tree = parser.parse(bytes(source_code, 'utf8'))\n",
    "            file_name = Path(java_file).stem\n",
    "            current_class = None\n",
    "            \n",
    "            def traverse_ast_with_metadata(node, depth=0):\n",
    "                nonlocal current_class\n",
    "                \n",
    "                if node.type == 'class_declaration':\n",
    "                    class_info = extract_class_info_with_metadata(node, source_code)\n",
    "                    class_name = class_info['class_name']\n",
    "                    \n",
    "                    if class_name:\n",
    "                        current_class = class_name\n",
    "                        \n",
    "                        # Add comprehensive class metadata\n",
    "                        metadata.add_class_metadata(\n",
    "                            class_name, str(java_file), app_name,\n",
    "                            parent_class=class_info['parent_class'],\n",
    "                            interfaces=class_info['interfaces'],\n",
    "                            annotations=class_info['annotations'],\n",
    "                            access_modifier=class_info['access_modifier'],\n",
    "                            is_abstract=class_info['is_abstract'],\n",
    "                            is_final=class_info['is_final'],\n",
    "                            package=metadata.file_metadata.get(str(java_file), {}).get('package_name')\n",
    "                        )\n",
    "                        \n",
    "                        # Register class\n",
    "                        class_registry[class_name] = {\n",
    "                            'file_path': str(java_file),\n",
    "                            'application': app_name,\n",
    "                            'extends': class_info['parent_class']\n",
    "                        }\n",
    "                        \n",
    "                        # FILE → CLASS relationship with metadata\n",
    "                        ast_relationships.append({\n",
    "                            'source_node': file_name,\n",
    "                            'source_type': 'file',\n",
    "                            'destination_node': class_name,\n",
    "                            'destination_type': 'class',\n",
    "                            'relationship': 'contains',\n",
    "                            'file_path': str(java_file),\n",
    "                            'application': app_name,\n",
    "                            'metadata': json.dumps(class_info)\n",
    "                        })\n",
    "                        \n",
    "                        # CLASS → EXTENDS relationship with metadata\n",
    "                        if class_info['parent_class']:\n",
    "                            ast_relationships.append({\n",
    "                                'source_node': class_name,\n",
    "                                'source_type': 'class',\n",
    "                                'destination_node': class_info['parent_class'],\n",
    "                                'destination_type': 'class',\n",
    "                                'relationship': 'extends',\n",
    "                                'file_path': str(java_file),\n",
    "                                'application': app_name,\n",
    "                                'metadata': json.dumps({'inheritance_type': 'extends'})\n",
    "                            })\n",
    "                        \n",
    "                        # Extract global variables (fields) with metadata\n",
    "                        global_vars = extract_global_variables_with_metadata(node, source_code, class_name)\n",
    "                        for var_info in global_vars:\n",
    "                            if var_info['name'] and is_meaningful_variable(var_info['name']):\n",
    "                                # Create standardized variable name\n",
    "                                standardized_var = f\"{class_name}.field.{var_info['name']}\"\n",
    "                                \n",
    "                                # Add variable metadata\n",
    "                                metadata.add_variable_metadata(\n",
    "                                    standardized_var, class_name, 'field', str(java_file), app_name,\n",
    "                                    original_name=var_info['name'],\n",
    "                                    data_type=var_info['data_type'],\n",
    "                                    access_modifier=var_info['access_modifier'],\n",
    "                                    is_static=var_info['is_static'],\n",
    "                                    is_final=var_info['is_final'],\n",
    "                                    annotations=var_info['annotations'],\n",
    "                                    storage_type=var_info['storage_type'],\n",
    "                                    definition_location=f\"{class_name} class field\",\n",
    "                                    scope='global'\n",
    "                                )\n",
    "                                \n",
    "                                # Register variable\n",
    "                                variable_registry[standardized_var] = {\n",
    "                                    'original_name': var_info['name'],\n",
    "                                    'class': class_name,\n",
    "                                    'method': 'field',\n",
    "                                    'file_path': str(java_file),\n",
    "                                    'application': app_name\n",
    "                                }\n",
    "                                \n",
    "                                # CLASS → VARIABLE relationship with metadata\n",
    "                                ast_relationships.append({\n",
    "                                    'source_node': class_name,\n",
    "                                    'source_type': 'class',\n",
    "                                    'destination_node': standardized_var,\n",
    "                                    'destination_type': 'variable',\n",
    "                                    'relationship': 'declares_variable',\n",
    "                                    'file_path': str(java_file),\n",
    "                                    'application': app_name,\n",
    "                                    'metadata': json.dumps(var_info)\n",
    "                                })\n",
    "                \n",
    "                elif node.type == 'method_declaration' and current_class:\n",
    "                    method_info = extract_method_info_with_metadata(node, source_code)\n",
    "                    method_name = method_info['method_name']\n",
    "                    \n",
    "                    if method_name:\n",
    "                        # Register method with metadata\n",
    "                        method_key = f\"{current_class}.{method_name}\"\n",
    "                        metadata.add_method_metadata(\n",
    "                            method_key, current_class, str(java_file), app_name,\n",
    "                            return_type=method_info['return_type'],\n",
    "                            parameters=method_info['parameters'],\n",
    "                            annotations=method_info['annotations'],\n",
    "                            access_modifier=method_info['access_modifier'],\n",
    "                            is_static=method_info['is_static'],\n",
    "                            is_abstract=method_info['is_abstract'],\n",
    "                            is_final=method_info['is_final'],\n",
    "                            throws_exceptions=method_info['throws_exceptions']\n",
    "                        )\n",
    "                        \n",
    "                        method_registry[method_key] = {\n",
    "                            'class': current_class,\n",
    "                            'file_path': str(java_file),\n",
    "                            'application': app_name,\n",
    "                            'parameters': [p['name'] for p in method_info['parameters']]\n",
    "                        }\n",
    "                        \n",
    "                        # CLASS → METHOD relationship with metadata\n",
    "                        ast_relationships.append({\n",
    "                            'source_node': current_class,\n",
    "                            'source_type': 'class',\n",
    "                            'destination_node': method_name,\n",
    "                            'destination_type': 'method',\n",
    "                            'relationship': 'declares',\n",
    "                            'file_path': str(java_file),\n",
    "                            'application': app_name,\n",
    "                            'metadata': json.dumps(method_info)\n",
    "                        })\n",
    "                \n",
    "                # Continue traversing\n",
    "                for child in node.children:\n",
    "                    traverse_ast_with_metadata(child, depth + 1)\n",
    "            \n",
    "            traverse_ast_with_metadata(tree.root_node)\n",
    "\n",
    "all_relationships.extend(ast_relationships)\n",
    "print(f'✅ AST analysis complete. Added {len(ast_relationships)} relationships with metadata')\n",
    "print(f'📊 Total relationships: {len(all_relationships)}')\n",
    "print(f'📊 Registered: {len(class_registry)} classes, {len(method_registry)} methods, {len(variable_registry)} global variables')"
   ]
  }
 ],
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "stage4_global_variable_tracking",
   "metadata": {},
   "outputs": [],
   "source": [
    "# ========== STAGE 4: GLOBAL VARIABLE TRACKING WITH TRANSFORMATION METADATA ==========\n",
    "\n",
    "def analyze_variable_transformations_with_metadata():\n",
    "    '''Analyze global variable transformations with comprehensive metadata'''\n",
    "    print('🔗 Analyzing global variable transformations with metadata...')\n",
    "    \n",
    "    transformation_relationships = []\n",
    "    \n",
    "    # Analyze transformations for each application\n",
    "    for app_name in APPLICATIONS.keys():\n",
    "        app_path = BASE_PATH / app_name\n",
    "        if not app_path.exists():\n",
    "            continue\n",
    "        \n",
    "        java_files = list(app_path.rglob('*.java'))\n",
    "        \n",
    "        for java_file in java_files:\n",
    "            try:\n",
    "                with open(java_file, 'r', encoding='utf-8') as f:\n",
    "                    source_code = f.read()\n",
    "            except Exception:\n",
    "                continue\n",
    "            \n",
    "            file_name = Path(java_file).stem\n",
    "            \n",
    "            # Find global variable usage patterns\n",
    "            global_var_patterns = [\n",
    "                # Service injection patterns\n",
    "                r'@Autowired\\s+(?:private|protected|public)?\\s*(\\w+)\\s+(\\w+)',\n",
    "                r'@Inject\\s+(?:private|protected|public)?\\s*(\\w+)\\s+(\\w+)',\n",
    "                # Field declarations\n",
    "                r'(?:private|protected|public)\\s+(?:static\\s+)?(?:final\\s+)?(\\w+)\\s+(\\w+)\\s*[;=]',\n",
    "                # Repository/Service usage\n",
    "                r'(\\w+)\\s*=\\s*(\\w+Repository|\\w+Service)\\.',\n",
    "                r'(\\w+)\\s*=\\s*(\\w+)\\.(save|find|get|create|update|delete)\\(',\n",
    "            ]\n",
    "            \n",
    "            transformations_found = []\n",
    "            \n",
    "            for pattern in global_var_patterns:\n",
    "                matches = re.findall(pattern, source_code, re.MULTILINE | re.IGNORECASE)\n",
    "                for match in matches:\n",
    "                    if len(match) >= 2:\n",
    "                        if isinstance(match, tuple):\n",
    "                            var_type = match[0] if len(match) > 1 else 'Object'\n",
    "                            var_name = match[1] if len(match) > 1 else match[0]\n",
    "                        else:\n",
    "                            var_type = 'Object'\n",
    "                            var_name = match\n",
    "                        \n",
    "                        if is_meaningful_variable(var_name) and is_global_variable(var_name, source_code):\n",
    "                            transformations_found.append({\n",
    "                                'variable': var_name,\n",
    "                                'type': var_type,\n",
    "                                'pattern': pattern,\n",
    "                                'context': 'global_field'\n",
    "                            })\n",
    "            \n",
    "            # Create transformation relationships with metadata\n",
    "            for i, transform in enumerate(transformations_found):\n",
    "                var_name = transform['variable']\n",
    "                var_type = transform['type']\n",
    "                \n",
    "                # Find the class this variable belongs to\n",
    "                class_name = file_name  # Default to file name\n",
    "                for registered_class in class_registry.keys():\n",
    "                    if class_registry[registered_class]['file_path'] == str(java_file):\n",
    "                        class_name = registered_class\n",
    "                        break\n",
    "                \n",
    "                # Create standardized variable name\n",
    "                standardized_var = f\"{class_name}.field.{var_name}\"\n",
    "                \n",
    "                # Update variable metadata with transformation info\n",
    "                if standardized_var in metadata.variable_metadata:\n",
    "                    current_transformations = metadata.variable_metadata[standardized_var].get('transformations', [])\n",
    "                    current_transformations.append({\n",
    "                        'type': 'field_usage',\n",
    "                        'pattern': transform['pattern'],\n",
    "                        'context': transform['context'],\n",
    "                        'data_type': var_type,\n",
    "                        'file': str(java_file)\n",
    "                    })\n",
    "                    metadata.variable_metadata[standardized_var]['transformations'] = current_transformations\n",
    "                    metadata.variable_metadata[standardized_var]['usage_count'] += 1\n",
    "                else:\n",
    "                    # Add new variable metadata\n",
    "                    metadata.add_variable_metadata(\n",
    "                        standardized_var, class_name, 'field', str(java_file), app_name,\n",
    "                        original_name=var_name,\n",
    "                        data_type=var_type,\n",
    "                        storage_type='field',\n",
    "                        definition_location=f\"{class_name} class\",\n",
    "                        scope='global',\n",
    "                        transformations=[{\n",
    "                            'type': 'field_usage',\n",
    "                            'pattern': transform['pattern'],\n",
    "                            'context': transform['context'],\n",
    "                            'data_type': var_type,\n",
    "                            'file': str(java_file)\n",
    "                        }],\n",
    "                        usage_count=1\n",
    "                    )\n",
    "                \n",
    "                # Create transformation relationships between similar variables\n",
    "                if i > 0:\n",
    "                    prev_transform = transformations_found[i-1]\n",
    "                    prev_standardized = f\"{class_name}.field.{prev_transform['variable']}\"\n",
    "                    \n",
    "                    transformation_metadata = {\n",
    "                        'transformation_type': 'global_variable_flow',\n",
    "                        'source_pattern': prev_transform['pattern'],\n",
    "                        'target_pattern': transform['pattern'],\n",
    "                        'data_flow': f\"{prev_transform['type']} -> {var_type}\",\n",
    "                        'context': 'global_scope'\n",
    "                    }\n",
    "                    \n",
    "                    transformation_relationships.append({\n",
    "                        'source_node': prev_standardized,\n",
    "                        'source_type': 'variable',\n",
    "                        'destination_node': standardized_var,\n",
    "                        'destination_type': 'variable',\n",
    "                        'relationship': 'transforms_to',\n",
    "                        'file_path': str(java_file),\n",
    "                        'application': app_name,\n",
    "                        'metadata': json.dumps(transformation_metadata)\n",
    "                    })\n",
    "    \n",
    "    print(f'✅ Created {len(transformation_relationships)} global variable transformations with metadata')\n",
    "    return transformation_relationships\n",
    "\n",
    "def create_cross_class_variable_connections_with_metadata():\n",
    "    '''Create cross-class variable connections with metadata'''\n",
    "    print('🔗 Creating cross-class variable connections with metadata...')\n",
    "    \n",
    "    cross_connections = []\n",
    "    \n",
    "    # Group variables by original name across classes\n",
    "    original_name_groups = defaultdict(list)\n",
    "    for var_key, var_info in variable_registry.items():\n",
    "        original_name = var_info['original_name']\n",
    "        original_name_groups[original_name].append((var_key, var_info))\n",
    "    \n",
    "    # Create connections between classes that use the same variable names\n",
    "    for original_name, var_list in original_name_groups.items():\n",
    "        if len(var_list) > 1:\n",
    "            # Create data flow connections with metadata\n",
    "            for i in range(len(var_list) - 1):\n",
    "                source_var, source_info = var_list[i]\n",
    "                target_var, target_info = var_list[i + 1]\n",
    "                \n",
    "                if source_info['class'] != target_info['class']:\n",
    "                    # Create rich metadata for cross-class connection\n",
    "                    connection_metadata = {\n",
    "                        'connection_type': 'cross_class_variable_usage',\n",
    "                        'original_variable_name': original_name,\n",
    "                        'source_class': source_info['class'],\n",
    "                        'target_class': target_info['class'],\n",
    "                        'source_application': source_info['application'],\n",
    "                        'target_application': target_info['application'],\n",
    "                        'is_cross_application': source_info['application'] != target_info['application'],\n",
    "                        'data_flow_direction': f\"{source_info['class']} -> {target_info['class']}\"\n",
    "                    }\n",
    "                    \n",
    "                    cross_connections.append({\n",
    "                        'source_node': source_var,\n",
    "                        'source_type': 'variable',\n",
    "                        'destination_node': target_var,\n",
    "                        'destination_type': 'variable',\n",
    "                        'relationship': 'data_find',\n",
    "                        'file_path': source_info['file_path'],\n",
    "                        'application': source_info['application'],\n",
    "                        'metadata': json.dumps(connection_metadata)\n",
    "                    })\n",
    "    \n",
    "    print(f'✅ Created {len(cross_connections)} cross-class variable connections with metadata')\n",
    "    return cross_connections\n",
    "\n",
    "# Execute global variable tracking\n",
    "var_transformations = analyze_variable_transformations_with_metadata()\n",
    "cross_class_connections = create_cross_class_variable_connections_with_metadata()\n",
    "\n",
    "# Add to master list\n",
    "all_relationships.extend(var_transformations)\n",
    "all_relationships.extend(cross_class_connections)\n",
    "\n",
    "print(f'✅ Global variable tracking complete with rich metadata')\n",
    "print(f'📊 Total relationships: {len(all_relationships)}')\n",
    "print(f'📊 Global variables tracked: {len([v for v in variable_registry.keys() if \".field.\" in v])}')"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "stage4b_metadata_llm_prep",
   "metadata": {},
   "outputs": [],
   "source": [
    "# ========== STAGE 4B: METADATA-ENHANCED LLM PREPARATION ==========\n",
    "\n",
    "def build_metadata_enhanced_system_prompt(file_path, current_relationships, metadata_collector):\n",
    "    '''Build system prompt with comprehensive metadata context for LLM'''\n",
    "    \n",
    "    # Filter relationships for this specific file\n",
    "    file_relationships = [r for r in current_relationships if r.get('file_path') == file_path]\n",
    "    \n",
    "    # Build AST context from current relationships\n",
    "    ast_context = 'CURRENT AST RELATIONSHIPS:\\n'\n",
    "    for rel in file_relationships[:15]:  # Limit to avoid token overflow\n",
    "        metadata_info = ''\n",
    "        if rel.get('metadata'):\n",
    "            try:\n",
    "                meta = json.loads(rel['metadata'])\n",
    "                if 'parent_class' in meta and meta['parent_class']:\n",
    "                    metadata_info = f\" (extends: {meta['parent_class']})\"\n",
    "                elif 'data_type' in meta and meta['data_type']:\n",
    "                    metadata_info = f\" (type: {meta['data_type']})\"\n",
    "                elif 'return_type' in meta and meta['return_type']:\n",
    "                    metadata_info = f\" (returns: {meta['return_type']})\"\n",
    "            except:\n",
    "                pass\n",
    "        \n",
    "        ast_context += f\"{rel['source_type']}:{rel['source_node']} -[{rel['relationship']}]-> {rel['destination_type']}:{rel['destination_node']}{metadata_info}\\n\"\n",
    "    \n",
    "    # Build class metadata context\n",
    "    class_context = 'KNOWN CLASSES WITH METADATA:\\n'\n",
    "    for class_name, class_meta in metadata_collector.class_metadata.items():\n",
    "        inheritance_info = ''\n",
    "        if class_meta.get('parent_class'):\n",
    "            inheritance_info = f\" extends {class_meta['parent_class']}\"\n",
    "        if class_meta.get('interfaces'):\n",
    "            inheritance_info += f\" implements {', '.join(class_meta['interfaces'])}\"\n",
    "        \n",
    "        annotations_info = ''\n",
    "        if class_meta.get('annotations'):\n",
    "            annotations_info = f\" @{', @'.join([a.replace('@', '') for a in class_meta['annotations']])}\"\n",
    "        \n",
    "        class_context += f'- {class_name} (app: {class_meta[\"application\"]}){inheritance_info}{annotations_info}\\n'\n",
    "    \n",
    "    # Build method metadata context\n",
    "    method_context = 'KNOWN METHODS WITH METADATA:\\n'\n",
    "    for method_key, method_meta in list(metadata_collector.method_metadata.items())[:10]:  # Limit for token management\n",
    "        params_info = ''\n",
    "        if method_meta.get('parameters'):\n",
    "            param_strs = [f\"{p.get('type', 'Object')} {p.get('name', 'param')}\" for p in method_meta['parameters']]\n",
    "            params_info = f\"({', '.join(param_strs)})\"\n",
    "        \n",
    "        return_info = ''\n",
    "        if method_meta.get('return_type'):\n",
    "            return_info = f\" -> {method_meta['return_type']}\"\n",
    "        \n",
    "        annotations_info = ''\n",
    "        if method_meta.get('annotations'):\n",
    "            annotations_info = f\" @{', @'.join([a.replace('@', '') for a in method_meta['annotations']])}\"\n",
    "        \n",
    "        method_context += f'- {method_key}{params_info}{return_info}{annotations_info}\\n'\n",
    "    \n",
    "    # Build global variable metadata context\n",
    "    variable_context = 'GLOBAL VARIABLES WITH METADATA:\\n'\n",
    "    global_vars = {k: v for k, v in metadata_collector.variable_metadata.items() if v.get('scope') == 'global'}\n",
    "    for var_key, var_meta in list(global_vars.items())[:10]:  # Limit for token management\n",
    "        type_info = ''\n",
    "        if var_meta.get('data_type'):\n",
    "            type_info = f\" ({var_meta['data_type']})\"\n",
    "        \n",
    "        annotations_info = ''\n",
    "        if var_meta.get('annotations'):\n",
    "            annotations_info = f\" @{', @'.join([a.replace('@', '') for a in var_meta['annotations']])}\"\n",
    "        \n",
    "        transformations_info = ''\n",
    "        if var_meta.get('transformations'):\n",
    "            transform_count = len(var_meta['transformations'])\n",
    "            transformations_info = f\" [{transform_count} transformations]\"\n",
    "        \n",
    "        variable_context += f'- {var_key}{type_info}{annotations_info}{transformations_info}\\n'\n",
    "    \n",
    "    prompt = f\"\"\"\n",
    "You are a Java code lineage extraction engine with RICH METADATA CONTEXT. Extract relationships using this comprehensive context:\n",
    "\n",
    "METADATA-ENHANCED CONTEXT:\n",
    "{class_context}\n",
    "\n",
    "{method_context}\n",
    "\n",
    "{variable_context}\n",
    "\n",
    "{ast_context}\n",
    "\n",
    "ENHANCED EXTRACTION RULES WITH METADATA:\n",
    "1. Use SIMPLE names only (remove prefixes like \"method:\", \"class:\", etc.)\n",
    "2. PRIORITIZE relationships based on metadata:\n",
    "   - class -[extends]-> class (use inheritance metadata)\n",
    "   - class -[declares]-> method (use method metadata)\n",
    "   - class -[declares_variable]-> variable (use field metadata)\n",
    "   - method -[uses]-> variable (focus on global variables only)\n",
    "   - variable -[transforms_to]-> variable (use transformation metadata)\n",
    "   - class -[exposes]-> endpoint (extract from annotations like @RequestMapping)\n",
    "   - class -[data_find]-> data (extract from JPA annotations like @Entity, @Table)\n",
    "3. Extract REST API endpoints from Spring annotations (@GetMapping, @PostMapping, etc.)\n",
    "4. Extract database entities from JPA annotations (@Entity, @Table, @Repository)\n",
    "5. Focus on GLOBAL variables only (class fields, injected services)\n",
    "6. Use metadata to determine relationship strength and importance\n",
    "7. Standardize variable format: ClassName.field.variableName (global only)\n",
    "8. Leverage inheritance metadata for better class relationships\n",
    "9. Use annotation metadata for API and data layer detection\n",
    "\n",
    "Extract triples in format:\n",
    "[SourceType]:SourceName -[RELATIONSHIP]-> [TargetType]:TargetName\n",
    "\n",
    "Return ONLY the triples, no explanations.\n",
    "\"\"\"\n",
    "    return prompt\n",
    "\n",
    "# Prepare documents for metadata-enhanced LLM processing\n",
    "print('📄 Preparing documents for metadata-enhanced LLM processing...')\n",
    "\n",
    "from langchain.text_splitter import Language as LC_Language\n",
    "\n",
    "splitter = RecursiveCharacterTextSplitter.from_language(\n",
    "    language=LC_Language.JAVA,\n",
    "    chunk_size=3500,  # Slightly smaller to accommodate metadata\n",
    "    chunk_overlap=200\n",
    ")\n",
    "\n",
    "java_docs, split_docs = [], []\n",
    "\n",
    "for app_name in APPLICATIONS.keys():\n",
    "    app_path = BASE_PATH / app_name\n",
    "    if app_path.exists():\n",
    "        for java_file in app_path.rglob('*.java'):\n",
    "            try:\n",
    "                loader = TextLoader(str(java_file), encoding='utf-8')\n",
    "                docs = loader.load()\n",
    "                java_docs.extend(docs)\n",
    "            except Exception as e:\n",
    "                continue\n",
    "\n",
    "for doc in java_docs:\n",
    "    split_docs.extend(splitter.split_documents([doc]))\n",
    "\n",
    "print(f'📄 Prepared {len(split_docs)} document chunks for metadata-enhanced LLM processing')\n",
    "print(f'📊 Metadata available: {len(metadata.class_metadata)} classes, {len(metadata.method_metadata)} methods, {len(metadata.variable_metadata)} variables')"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "stage4c_metadata_llm_processing",
   "metadata": {},
   "outputs": [],
   "source": [
    "# ========== STAGE 4C: METADATA-DRIVEN LLM PROCESSING ==========\n",
    "\n",
    "def normalize_entity_with_metadata(entity_name, entity_type, metadata_collector):\n",
    "    '''Normalize entity names using metadata for consistency'''\n",
    "    if not entity_name:\n",
    "        return entity_name\n",
    "    \n",
    "    # Remove prefixes\n",
    "    prefixes = ['method:', 'class:', 'variable:', 'field:', 'table:', 'endpoint:']\n",
    "    for prefix in prefixes:\n",
    "        if entity_name.lower().startswith(prefix):\n",
    "            entity_name = entity_name[len(prefix):]\n",
    "    \n",
    "    # Remove file extensions\n",
    "    entity_name = re.sub(r'\\.(java|class)$', '', entity_name, flags=re.IGNORECASE)\n",
    "    \n",
    "    # Use metadata for consistency\n",
    "    if entity_type == 'class':\n",
    "        # Match with class metadata for exact naming\n",
    "        for class_name in metadata_collector.class_metadata.keys():\n",
    "            if entity_name.lower() == class_name.lower():\n",
    "                return class_name\n",
    "    \n",
    "    elif entity_type == 'method':\n",
    "        # Match with method metadata\n",
    "        for method_key in metadata_collector.method_metadata.keys():\n",
    "            method_name = method_key.split('.')[-1]\n",
    "            if entity_name.lower() == method_name.lower():\n",
    "                return method_name\n",
    "    \n",
    "    elif entity_type == 'variable':\n",
    "        # For variables, check if it should be standardized\n",
    "        if '.' not in entity_name:\n",
    "            # Look for matching variable in metadata\n",
    "            for var_key, var_meta in metadata_collector.variable_metadata.items():\n",
    "                if var_meta.get('original_name', '').lower() == entity_name.lower():\n",
    "                    return var_key  # Return standardized name\n",
    "    \n",
    "    return entity_name\n",
    "\n",
    "def extract_api_endpoints_from_metadata(chunk_content, class_name, metadata_collector):\n",
    "    '''Extract API endpoints using metadata context'''\n",
    "    endpoints = []\n",
    "    \n",
    "    # Check class metadata for controller annotations\n",
    "    class_meta = metadata_collector.class_metadata.get(class_name, {})\n",
    "    is_controller = any('@Controller' in str(ann) or '@RestController' in str(ann) \n",
    "                       for ann in class_meta.get('annotations', []))\n",
    "    \n",
    "    if is_controller:\n",
    "        # Extract base path from class-level RequestMapping\n",
    "        base_path = ''\n",
    "        for ann in class_meta.get('annotations', []):\n",
    "            if '@RequestMapping' in str(ann):\n",
    "                path_match = re.search(r'[\"\\']([^\"\\']*/[^\"\\']*)[\"\\'']', str(ann))\n",
    "                if path_match:\n",
    "                    base_path = path_match.group(1)\n",
    "        \n",
    "        # Extract method-level mappings\n",
    "        mapping_patterns = [\n",
    "            r'@GetMapping\\([\"\\']([^\"\\']*)[\"\\'']\\)',\n",
    "            r'@PostMapping\\([\"\\']([^\"\\']*)[\"\\'']\\)',\n",
    "            r'@PutMapping\\([\"\\']([^\"\\']*)[\"\\'']\\)',\n",
    "            r'@DeleteMapping\\([\"\\']([^\"\\']*)[\"\\'']\\)',\n",
    "            r'@RequestMapping\\([^)]*value\\s*=\\s*[\"\\']([^\"\\']*)[\"\\''][^)]*method\\s*=\\s*RequestMethod\\.(\\w+)',\n",
    "        ]\n",
    "        \n",
    "        for pattern in mapping_patterns:\n",
    "            matches = re.findall(pattern, chunk_content)\n",
    "            for match in matches:\n",
    "                if isinstance(match, tuple):\n",
    "                    path = match[0]\n",
    "                    method = match[1] if len(match) > 1 else 'GET'\n",
    "                else:\n",
    "                    path = match\n",
    "                    method = 'GET'  # Default\n",
    "                \n",
    "                full_path = base_path + path if not path.startswith('/') else path\n",
    "                endpoint = f\"{method.upper()} {full_path}\"\n",
    "                endpoints.append(endpoint)\n",
    "    \n",
    "    return endpoints\n",
    "\n",
    "def extract_data_entities_from_metadata(chunk_content, class_name, metadata_collector):\n",
    "    '''Extract data entities using metadata context'''\n",
    "    entities = []\n",
    "    \n",
    "    # Check class metadata for JPA annotations\n",
    "    class_meta = metadata_collector.class_metadata.get(class_name, {})\n",
    "    annotations = class_meta.get('annotations', [])\n",
    "    \n",
    "    for ann in annotations:\n",
    "        ann_str = str(ann)\n",
    "        if '@Entity' in ann_str:\n",
    "            entities.append(f\"entity_{class_name}\")\n",
    "        elif '@Table' in ann_str:\n",
    "            # Extract table name\n",
    "            table_match = re.search(r'name\\s*=\\s*[\"\\']([^\"\\']*)[\"\\'']', ann_str)\n",
    "            if table_match:\n",
    "                entities.append(f\"table_{table_match.group(1)}\")\n",
    "            else:\n",
    "                entities.append(f\"table_{class_name.lower()}\")\n",
    "        elif '@Repository' in ann_str:\n",
    "            entities.append(f\"repository_{class_name}\")\n",
    "    \n",
    "    return entities\n",
    "\n",
    "# Initialize metadata-enhanced LLM lineage collection\n",
    "all_metadata_llm_lineage = []\n",
    "\n",
    "print('🤖 Starting metadata-driven LLM extraction...')\n",
    "\n",
    "for chunk in tqdm(split_docs, desc='Metadata-Enhanced LLM Processing'):\n",
    "    file_path = chunk.metadata.get('source')\n",
    "    \n",
    "    # Build metadata-enhanced system prompt\n",
    "    system_prompt = build_metadata_enhanced_system_prompt(file_path, all_relationships, metadata)\n",
    "    \n",
    "    # Extract class name from file path for context\n",
    "    file_name = os.path.basename(file_path) if file_path else 'unknown'\n",
    "    class_name = file_name.replace('.java', '') if file_name.endswith('.java') else file_name\n",
    "    \n",
    "    # Determine application\n",
    "    app_name = 'ServiceBolt' if 'ServiceBolt' in str(file_path) else 'UnifiedBolt'\n",
    "    \n",
    "    # Extract API endpoints using metadata\n",
    "    api_endpoints = extract_api_endpoints_from_metadata(chunk.page_content, class_name, metadata)\n",
    "    for endpoint in api_endpoints:\n",
    "        all_metadata_llm_lineage.append({\n",
    "            'source_node': class_name,\n",
    "            'source_type': 'class',\n",
    "            'destination_node': endpoint,\n",
    "            'destination_type': 'endpoint',\n",
    "            'relationship': 'exposes',\n",
    "            'file_path': file_path,\n",
    "            'application': app_name,\n",
    "            'metadata': json.dumps({\n",
    "                'endpoint_type': 'REST_API',\n",
    "                'extracted_from': 'metadata_annotations',\n",
    "                'class_metadata': metadata.class_metadata.get(class_name, {})\n",
    "            })\n",
    "        })\n",
    "    \n",
    "    # Extract data entities using metadata\n",
    "    data_entities = extract_data_entities_from_metadata(chunk.page_content, class_name, metadata)\n",
    "    for entity in data_entities:\n",
    "        all_metadata_llm_lineage.append({\n",
    "            'source_node': class_name,\n",
    "            'source_type': 'class',\n",
    "            'destination_node': entity,\n",
    "            'destination_type': 'data',\n",
    "            'relationship': 'data_find',\n",
    "            'file_path': file_path,\n",
    "            'application': app_name,\n",
    "            'metadata': json.dumps({\n",
    "                'entity_type': 'JPA_ENTITY',\n",
    "                'extracted_from': 'metadata_annotations',\n",
    "                'class_metadata': metadata.class_metadata.get(class_name, {})\n",
    "            })\n",
    "        })\n",
    "    \n",
    "    # LLM Graph Transformer with metadata-enhanced context\n",
    "    transformer = LLMGraphTransformer(\n",
    "        llm=llm,\n",
    "        additional_instructions=system_prompt,\n",
    "        allowed_nodes=['project', 'application', 'folder', 'file', 'class', 'method', 'interface', 'variable', 'endpoint', 'data'],\n",
    "        allowed_relationships=[\n",
    "            ('project', 'contains', 'application'),\n",
    "            ('application', 'contains', 'folder'),\n",
    "            ('folder', 'contains', 'folder'),\n",
    "            ('folder', 'contains', 'file'),\n",
    "            ('file', 'contains', 'class'),\n",
    "            ('file', 'declares', 'interface'),\n",
    "            ('class', 'declares', 'method'),\n",
    "            ('class', 'declares_variable', 'variable'),\n",
    "            ('class', 'exposes', 'endpoint'),\n",
    "            ('class', 'extends', 'class'),\n",
    "            ('class', 'implements', 'interface'),\n",
    "            ('class', 'has_field', 'variable'),\n",
    "            ('method', 'uses', 'variable'),\n",
    "            ('variable', 'transforms_to', 'variable'),\n",
    "            ('class', 'data_find', 'data'),\n",
    "            ('method', 'data_find', 'data'),\n",
    "        ],\n",
    "        strict_mode=True,\n",
    "        node_properties=False,\n",
    "        relationship_properties=False,\n",
    "    )\n",
    "    \n",
    "    try:\n",
    "        graph_docs = transformer.convert_to_graph_documents([chunk])\n",
    "        for gd in graph_docs:\n",
    "            for rel in gd.relationships:\n",
    "                s_node = rel.source.id.strip()\n",
    "                s_type = rel.source.type.strip().lower()\n",
    "                t_node = rel.target.id.strip()\n",
    "                t_type = rel.target.type.strip().lower()\n",
    "                rel_type = rel.type.strip().lower()\n",
    "\n",
    "                # Normalize entities using metadata\n",
    "                s_node = normalize_entity_with_metadata(s_node, s_type, metadata)\n",
    "                t_node = normalize_entity_with_metadata(t_node, t_type, metadata)\n",
    "                \n",
    "                # Skip if empty or invalid\n",
    "                if not s_node or not t_node or s_node == t_node:\n",
    "                    continue\n",
    "                \n",
    "                # Standardize variable names if they're variables (global only)\n",
    "                if s_type == 'variable' and '.' not in s_node:\n",
    "                    # Check if it's a global variable using metadata\n",
    "                    is_global = any(s_node in var_meta.get('original_name', '') \n",
    "                                  for var_meta in metadata.variable_metadata.values() \n",
    "                                  if var_meta.get('scope') == 'global')\n",
    "                    if is_global:\n",
    "                        s_node = f\"{class_name}.field.{s_node}\"\n",
    "                    else:\n",
    "                        continue  # Skip local variables\n",
    "                \n",
    "                if t_type == 'variable' and '.' not in t_node:\n",
    "                    # Check if it's a global variable using metadata\n",
    "                    is_global = any(t_node in var_meta.get('original_name', '') \n",
    "                                  for var_meta in metadata.variable_metadata.values() \n",
    "                                  if var_meta.get('scope') == 'global')\n",
    "                    if is_global:\n",
    "                        t_node = f\"{class_name}.field.{t_node}\"\n",
    "                    else:\n",
    "                        continue  # Skip local variables\n",
    "                \n",
    "                # Create metadata for this relationship\n",
    "                relationship_metadata = {\n",
    "                    'extracted_by': 'metadata_enhanced_llm',\n",
    "                    'source_metadata': metadata.class_metadata.get(s_node, {}) if s_type == 'class' else {},\n",
    "                    'target_metadata': metadata.class_metadata.get(t_node, {}) if t_type == 'class' else {},\n",
    "                    'confidence': 'high'  # Metadata-enhanced extraction has higher confidence\n",
    "                }\n",
    "                \n",
    "                all_metadata_llm_lineage.append({\n",
    "                    'source_node': s_node,\n",
    "                    'source_type': s_type,\n",
    "                    'destination_node': t_node,\n",
    "                    'destination_type': t_type,\n",
    "                    'relationship': rel_type,\n",
    "                    'file_path': file_path,\n",
    "                    'application': app_name,\n",
    "                    'metadata': json.dumps(relationship_metadata)\n",
    "                })\n",
    "    except Exception as e:\n",
    "        print(f'⚠️ Metadata-enhanced LLM processing error for {file_path}: {e}')\n",
    "        continue\n",
    "\n",
    "# Add metadata-enhanced LLM relationships to master list\n",
    "all_relationships.extend(all_metadata_llm_lineage)\n",
    "\n",
    "print(f'✅ Metadata-driven LLM processing complete. Added {len(all_metadata_llm_lineage)} relationships')\n",
    "print(f'📊 Total relationships: {len(all_relationships)}')\n",
    "\n",
    "if len(all_metadata_llm_lineage) > 0:\n",
    "    llm_df = pd.DataFrame(all_metadata_llm_lineage)\n",
    "    print(f'   🔗 Metadata-enhanced LLM relationship types: {llm_df[\"relationship\"].value_counts().to_dict()}')\n",
    "    print(f'   🏢 Metadata-enhanced LLM applications: {llm_df[\"application\"].value_counts().to_dict()}')\n",
    "    \n",
    "    # Show API endpoints and data entities extracted\n",
    "    endpoints = llm_df[llm_df['destination_type'] == 'endpoint']['destination_node'].tolist()\n",
    "    data_entities = llm_df[llm_df['destination_type'] == 'data']['destination_node'].tolist()\n",
    "    print(f'   🌐 API endpoints extracted: {len(endpoints)}')\n",
    "    print(f'   🗄️ Data entities extracted: {len(data_entities)}')"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "stage5_metadata_cleaning",
   "metadata": {},
   "outputs": [],
   "source": [
    "# ========== STAGE 5: DATA CLEANING AND METADATA VALIDATION ==========\n",
    "\n",
    "def clean_and_validate_data_with_metadata():\n",
    "    '''Clean, validate, and prepare data with metadata for Neo4j'''\n",
    "    print('🧹 Cleaning and validating data with metadata...')\n",
    "    \n",
    "    # Convert to DataFrame for easier processing\n",
    "    df = pd.DataFrame(all_relationships)\n",
    "    \n",
    "    print(f'📊 Initial dataset: {len(df)} relationships')\n",
    "    \n",
    "    # 1. Handle missing values\n",
    "    df['file_path'] = df['file_path'].fillna('')\n",
    "    df['application'] = df['application'].fillna('Unknown')\n",
    "    df['metadata'] = df['metadata'].fillna('{}')\n",
    "    \n",
    "    # 2. Remove duplicates\n",
    "    initial_count = len(df)\n",
    "    df = df.drop_duplicates(subset=['source_node', 'source_type', 'destination_node', 'destination_type', 'relationship'])\n",
    "    print(f'🔄 Removed {initial_count - len(df)} duplicates')\n",
    "    \n",
    "    # 3. Validate node names\n",
    "    def is_valid_node_name(name):\n",
    "        if pd.isna(name) or str(name).strip() == '':\n",
    "            return False\n",
    "        return True\n",
    "    \n",
    "    valid_mask = (df['source_node'].apply(is_valid_node_name) & \n",
    "                  df['destination_node'].apply(is_valid_node_name))\n",
    "    \n",
    "    invalid_count = len(df) - valid_mask.sum()\n",
    "    if invalid_count > 0:\n",
    "        print(f'🔄 Removed {invalid_count} relationships with invalid node names')\n",
    "        df = df[valid_mask]\n",
    "    \n",
    "    # 4. Validate metadata JSON\n",
    "    def is_valid_metadata(meta_str):\n",
    "        try:\n",
    "            json.loads(str(meta_str))\n",
    "            return True\n",
    "        except:\n",
    "            return False\n",
    "    \n",
    "    # Fix invalid metadata\n",
    "    invalid_metadata_mask = ~df['metadata'].apply(is_valid_metadata)\n",
    "    df.loc[invalid_metadata_mask, 'metadata'] = '{}'\n",
    "    print(f'🔄 Fixed {invalid_metadata_mask.sum()} invalid metadata entries')\n",
    "    \n",
    "    # 5. Standardize relationship types\n",
    "    relationship_mapping = {\n",
    "        'contains': 'CONTAINS',\n",
    "        'declares': 'DECLARES',\n",
    "        'declares_variable': 'DECLARES_VARIABLE',\n",
    "        'extends': 'EXTENDS',\n",
    "        'implements': 'IMPLEMENTS',\n",
    "        'uses': 'USES',\n",
    "        'transforms_to': 'TRANSFORMS_TO',\n",
    "        'data_find': 'DATA_FIND',\n",
    "        'exposes': 'EXPOSES',\n",
    "        'has_field': 'HAS_FIELD'\n",
    "    }\n",
    "    \n",
    "    df['neo4j_relationship'] = df['relationship'].map(relationship_mapping)\n",
    "    df['neo4j_relationship'] = df['neo4j_relationship'].fillna(df['relationship'].str.upper())\n",
    "    \n",
    "    # 6. Add node labels for Neo4j\n",
    "    node_type_mapping = {\n",
    "        'project': 'Project',\n",
    "        'application': 'Application',\n",
    "        'folder': 'Folder',\n",
    "        'file': 'File',\n",
    "        'class': 'Class',\n",
    "        'method': 'Method',\n",
    "        'interface': 'Interface',\n",
    "        'variable': 'Variable',\n",
    "        'endpoint': 'Endpoint',\n",
    "        'data': 'Data'\n",
    "    }\n",
    "    \n",
    "    df['source_label'] = df['source_type'].map(node_type_mapping)\n",
    "    df['destination_label'] = df['destination_type'].map(node_type_mapping)\n",
    "    \n",
    "    # 7. Validate global variables only\n",
    "    global_vars = df[df['destination_type'] == 'variable']\n",
    "    properly_standardized = global_vars['destination_node'].str.contains(r'^[^.]+\\.field\\.[^.]+$', na=False)\n",
    "    non_global_vars = global_vars[~properly_standardized]\n",
    "    \n",
    "    if len(non_global_vars) > 0:\n",
    "        print(f'🔄 Removing {len(non_global_vars)} non-global variables')\n",
    "        df = df[~df.index.isin(non_global_vars.index)]\n",
    "    \n",
    "    # 8. Generate comprehensive statistics\n",
    "    print('\\n📊 Final Dataset Statistics:')\n",
    "    print(f'   Total relationships: {len(df)}')\n",
    "    print(f'   Relationship types: {df[\"relationship\"].value_counts().to_dict()}')\n",
    "    print(f'   Applications: {df[\"application\"].value_counts().to_dict()}')\n",
    "    print(f'   Node types: {df[\"source_type\"].value_counts().to_dict()}')\n",
    "    \n",
    "    # 9. Validate metadata richness\n",
    "    metadata_with_content = df[df['metadata'] != '{}']['metadata'].count()\n",
    "    print(f'   Relationships with metadata: {metadata_with_content}/{len(df)} ({metadata_with_content/len(df)*100:.1f}%)')\n",
    "    \n",
    "    # 10. Validate global variables\n",
    "    global_vars_final = df[df['destination_type'] == 'variable']['destination_node']\n",
    "    properly_standardized_final = global_vars_final.str.contains(r'^[^.]+\\.field\\.[^.]+$', na=False)\n",
    "    print(f'   Global variables (properly standardized): {properly_standardized_final.sum()}/{len(global_vars_final)}')\n",
    "    \n",
    "    # 11. API endpoints and data entities\n",
    "    endpoints_count = len(df[df['destination_type'] == 'endpoint'])\n",
    "    data_entities_count = len(df[df['destination_type'] == 'data'])\n",
    "    print(f'   API endpoints: {endpoints_count}')\n",
    "    print(f'   Data entities: {data_entities_count}')\n",
    "    \n",
    "    # 12. Export cleaned data with metadata\n",
    "    df.to_csv('oneinsights_v12_metadata_enhanced_relationships.csv', index=False)\n",
    "    print(f'✅ Exported metadata-enhanced dataset to oneinsights_v12_metadata_enhanced_relationships.csv')\n",
    "    \n",
    "    # 13. Export metadata summaries\n",
    "    metadata_summary = {\n",
    "        'class_metadata': metadata.class_metadata,\n",
    "        'method_metadata': metadata.method_metadata,\n",
    "        'variable_metadata': metadata.variable_metadata,\n",
    "        'file_metadata': metadata.file_metadata,\n",
    "        'folder_metadata': metadata.folder_metadata,\n",
    "        'app_metadata': metadata.app_metadata\n",
    "    }\n",
    "    \n",
    "    with open('oneinsights_v12_metadata_summary.json', 'w') as f:\n",
    "        json.dump(metadata_summary, f, indent=2, default=str)\n",
    "    print(f'✅ Exported metadata summary to oneinsights_v12_metadata_summary.json')\n",
    "    \n",
    "    return df\n",
    "\n",
    "# Clean the data with metadata\n",
    "df_clean_metadata = clean_and_validate_data_with_metadata()"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "stage6_neo4j_metadata_loading",
   "metadata": {},
   "outputs": [],
   "source": [
    "# ========== STAGE 6: NEO4J LOADING WITH METADATA PROPERTIES ==========\n",
    "\n",
    "def load_to_neo4j_with_metadata(df):\n",
    "    '''Load clean data to Neo4j with metadata properties'''\n",
    "    if graph is None:\n",
    "        print('❌ Neo4j connection not available')\n",
    "        return\n",
    "    \n",
    "    try:\n",
    "        print('🚀 Loading data to Neo4j with metadata properties...')\n",
    "        \n",
    "        # Clear existing data\n",
    "        print('🧹 Clearing existing data...')\n",
    "        graph.query('MATCH (n) DETACH DELETE n')\n",
    "        \n",
    "        # Load data in batches with metadata\n",
    "        batch_size = 50  # Smaller batches due to metadata\n",
    "        total_batches = len(df) // batch_size + (1 if len(df) % batch_size > 0 else 0)\n",
    "        \n",
    "        print(f'📊 Loading {len(df)} relationships in {total_batches} batches with metadata...')\n",
    "        \n",
    "        for i in tqdm(range(0, len(df), batch_size), desc='Loading to Neo4j with Metadata', total=total_batches):\n",
    "            batch = df.iloc[i:i+batch_size]\n",
    "            \n",
    "            for _, row in batch.iterrows():\n",
    "                try:\n",
    "                    # Parse metadata\n",
    "                    metadata_dict = {}\n",
    "                    try:\n",
    "                        metadata_dict = json.loads(row['metadata'])\n",
    "                    except:\n",
    "                        metadata_dict = {}\n",
    "                    \n",
    "                    # Create enhanced Cypher query with metadata\n",
    "                    source_props = {\n",
    "                        'name': str(row['source_node']),\n",
    "                        'application': str(row['application']),\n",
    "                        'node_type': str(row['source_type'])\n",
    "                    }\n",
    "                    \n",
    "                    target_props = {\n",
    "                        'name': str(row['destination_node']),\n",
    "                        'application': str(row['application']),\n",
    "                        'node_type': str(row['destination_type'])\n",
    "                    }\n",
    "                    \n",
    "                    # Add metadata properties based on node type\n",
    "                    if row['source_type'] == 'class' and row['source_node'] in metadata.class_metadata:\n",
    "                        class_meta = metadata.class_metadata[row['source_node']]\n",
    "                        source_props.update({\n",
    "                            'parent_class': class_meta.get('parent_class', ''),\n",
    "                            'package': class_meta.get('package', ''),\n",
    "                            'is_abstract': class_meta.get('is_abstract', False),\n",
    "                            'access_modifier': class_meta.get('access_modifier', 'public')\n",
    "                        })\n",
    "                    \n",
    "                    if row['destination_type'] == 'variable' and row['destination_node'] in metadata.variable_metadata:\n",
    "                        var_meta = metadata.variable_metadata[row['destination_node']]\n",
    "                        target_props.update({\n",
    "                            'data_type': var_meta.get('data_type', ''),\n",
    "                            'scope': var_meta.get('scope', 'global'),\n",
    "                            'storage_type': var_meta.get('storage_type', 'field'),\n",
    "                            'usage_count': var_meta.get('usage_count', 0)\n",
    "                        })\n",
    "                    \n",
    "                    # Create Cypher query with properties\n",
    "                    cypher = f\"\"\"\n",
    "                    MERGE (source:{row['source_label']} $source_props)\n",
    "                    MERGE (target:{row['destination_label']} $target_props)\n",
    "                    MERGE (source)-[:{row['neo4j_relationship']} {{metadata: $metadata}}]->(target)\n",
    "                    \"\"\"\n",
    "                    \n",
    "                    # Execute query\n",
    "                    graph.query(cypher, {\n",
    "                        'source_props': source_props,\n",
    "                        'target_props': target_props,\n",
    "                        'metadata': json.dumps(metadata_dict)\n",
    "                    })\n",
    "                \n",
    "                except Exception as e:\n",
    "                    continue\n",
    "        \n",
    "        # Create indexes for better performance\n",
    "        print('🔧 Creating indexes...')\n",
    "        indexes = [\n",
    "            'CREATE INDEX IF NOT EXISTS FOR (n:Project) ON (n.name)',\n",
    "            'CREATE INDEX IF NOT EXISTS FOR (n:Application) ON (n.name)',\n",
    "            'CREATE INDEX IF NOT EXISTS FOR (n:Class) ON (n.name)',\n",
    "            'CREATE INDEX IF NOT EXISTS FOR (n:Variable) ON (n.scope)',\n",
    "            'CREATE INDEX IF NOT EXISTS FOR (n:Endpoint) ON (n.name)',\n",
    "            'CREATE INDEX IF NOT EXISTS FOR (n:Data) ON (n.name)'\n",
    "        ]\n",
    "        \n",
    "        for index in indexes:\n",
    "            try:\n",
    "                graph.query(index)\n",
    "            except:\n",
    "                pass\n",
    "        \n",
    "        print('✅ Metadata-enhanced data successfully loaded to Neo4j!')\n",
    "        \n",
    "        # Verify the load with metadata\n",
    "        verify_neo4j_load_with_metadata()\n",
    "        \n",
    "    except Exception as e:\n",
    "        print(f'❌ Error loading to Neo4j: {e}')\n",
    "\n",
    "def verify_neo4j_load_with_metadata():\n",
    "    '''Verify the Neo4j load and show metadata-enhanced statistics'''\n",
    "    print('\\n🔍 Verifying Neo4j load with metadata...')\n",
    "    \n",
    "    try:\n",
    "        # Node statistics\n",
    "        node_stats = graph.query(\"\"\"\n",
    "        MATCH (n)\n",
    "        RETURN labels(n)[0] as node_type, count(*) as count\n",
    "        ORDER BY count DESC\n",
    "        \"\"\")\n",
    "        \n",
    "        print('📊 Node Statistics:')\n",
    "        for stat in node_stats:\n",
    "            print(f'   {stat[\"node_type\"]}: {stat[\"count\"]} nodes')\n",
    "        \n",
    "        # Project structure\n",
    "        project_structure = graph.query(\"\"\"\n",
    "        MATCH (p:Project)-[:CONTAINS]->(a:Application)\n",
    "        RETURN p.name as project, collect(a.name) as applications\n",
    "        \"\"\")\n",
    "        \n",
    "        print('\\n🏗️ Project Structure:')\n",
    "        for struct in project_structure:\n",
    "            print(f'   {struct[\"project\"]} → {struct[\"applications\"]}')\n",
    "        \n",
    "        # Global variables with metadata\n",
    "        sample_vars = graph.query(\"\"\"\n",
    "        MATCH (v:Variable)\n",
    "        WHERE v.scope = 'global' AND v.name CONTAINS '.field.'\n",
    "        RETURN v.name as variable_name, v.data_type as data_type\n",
    "        LIMIT 5\n",
    "        \"\"\")\n",
    "        \n",
    "        print('\\n🔗 Sample Global Variables with Metadata:')\n",
    "        for var in sample_vars:\n",
    "            print(f'   {var[\"variable_name\"]} ({var[\"data_type\"]})')\n",
    "        \n",
    "    except Exception as e:\n",
    "        print(f'❌ Error verifying Neo4j load: {e}')\n",
    "\n",
    "# Load to Neo4j with metadata\n",
    "load_to_neo4j_with_metadata(df_clean_metadata)\n",
    "\n",
    "print('\\n🎉 ONEINSIGHTS V12 METADATA-ENHANCED ANALYSIS COMPLETE!')\n",
    "print('\\n✅ Achievements:')\n",
    "print('   🏗️ Complete hierarchy with metadata')\n",
    "print('   🔗 OneInsights project connects to BOTH applications')\n",
    "print('   📊 Global variables standardized as ClassName.field.variableName')\n",
    "print('   🤖 Metadata-enhanced LLM processing')\n",
    "print('   🔍 AST + Metadata + LLM combined analysis')\n",
    "print('   📋 Comprehensive metadata for all node types')\n",
    "print('   🧹 Clean, validated data with metadata properties in Neo4j')\n",
    "print('   📁 Complete CSV and JSON exports for analysis')\n",
    "print('\\n🎯 Ready for advanced metadata-driven graph analysis!')"
   ]
  },
 "metadata": {
  "kernelspec": {
   "display_name": "Python 3",
   "language": "python",
   "name": "python3"
  },
  "language_info": {
   "codemirror_mode": {
    "name": "ipython",
    "version": 3
   },
   "file_extension": ".py",
   "mimetype": "text/x-python",
   "name": "python",
   "nbconvert_exporter": "python",
   "pygments_lexer": "ipython3",
   "version": "3.8.5"
  }
 },
 "nbformat": 4,
 "nbformat_minor": 5
}
