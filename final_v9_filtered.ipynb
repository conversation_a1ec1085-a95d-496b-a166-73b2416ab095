# ========== IMPORTS AND SETUP ==========
import os
from pathlib import Path
from tqdm import tqdm
import pandas as pd
import re
from collections import defaultdict
import warnings
warnings.filterwarnings('ignore')

from tree_sitter import Language, Parser
import tree_sitter_java as tsjava

from langchain_community.document_loaders import TextLoader
from langchain.text_splitter import RecursiveCharacterTextSplitter, Language as LC_Language
from langchain_google_genai import ChatGoogleGenerativeAI
from langchain_experimental.graph_transformers import LLMGraphTransformer
from langchain_community.graphs import Neo4jGraph

# ========== CONFIGURATION ==========
BASE_PATH = Path(r'C:/Shaik/sample/OneInsights')
NEO4J_URI = 'bolt://localhost:7687'
NEO4J_USER = 'neo4j'
NEO4J_PASSWORD = 'Test@7889'
NEO4J_DB = 'oneinsights-v9'
GOOGLE_API_KEY = 'AIzaSyAeSuntl3dxGqQhwxRG_jom1V_EjxEPSwc'

# ========== INITIALIZE COMPONENTS ==========
try:
    graph = Neo4jGraph(url=NEO4J_URI, username=NEO4J_USER, password=NEO4J_PASSWORD, database=NEO4J_DB)
    print('✅ Neo4j connection established')
except Exception as e:
    print(f'❌ Neo4j connection failed: {e}')
    graph = None

JAVA_LANGUAGE = Language(tsjava.language())
parser = Parser(JAVA_LANGUAGE)

llm = ChatGoogleGenerativeAI(
    model='gemini-2.0-flash-exp',
    temperature=0,
    google_api_key=GOOGLE_API_KEY
)

# ========== ENHANCED FILTERING FOR FOCUSED ANALYSIS ==========
NOISE_VARIABLES = {
    # Loop variables
    'i', 'j', 'k', 'l', 'm', 'n', 'x', 'y', 'z',
    # Common temporary variables
    'temp', 'tmp', 'obj', 'item', 'elem', 'node', 'val', 'value',
    # Common short variables
    'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w',
    # Reserved keywords
    'this', 'super', 'null', 'true', 'false', 'void', 'return',
    # Meaningless short names
    'it', 'ex', 'e1', 'e2', 'o1', 'o2'
}

APPLICATIONS = {
    'ServiceBolt': 'REST API Service Layer',
    'UnifiedBolt': 'Core Business Logic and Data Layer'
}

def is_meaningful_variable(var_name):
    '''Filter out noise variables and keep only meaningful ones'''
    if not var_name or len(var_name) < 2:
        return False
    
    if var_name.lower() in NOISE_VARIABLES:
        return False
    
    # Focus on business-meaningful variables
    if len(var_name) > 4:  # Increased threshold for more meaningful names
        return True
    
    meaningful_short = {'id', 'url', 'api', 'key', 'dto', 'dao', 'req', 'res', 'ctx', 'data'}
    return var_name.lower() in meaningful_short

def detect_application(file_path):
    '''Detect which application a file belongs to'''
    path_str = str(file_path).replace('\\', '/')
    if 'ServiceBolt' in path_str:
        return 'ServiceBolt'
    elif 'UnifiedBolt' in path_str:
        return 'UnifiedBolt'
    return 'Unknown'

print('🚀 Setup complete! Ready for focused analysis...')

# ========== STAGE 1: PROJECT + APPLICATION + FOLDER + FILE HIERARCHY ==========

def extract_focused_hierarchy(base_path):
    '''Extract focused hierarchy: PROJECT -> APPLICATIONS -> FOLDERS -> FILES'''
    records = []
    base_path = os.path.abspath(base_path)
    project_name = os.path.basename(base_path)
    processed_folders = set()
    detected_apps = set()

    # Create PROJECT node (root)
    for root, dirs, files in os.walk(base_path):
        rel_root = os.path.relpath(root, base_path)
        app_name = detect_application(root)
        
        if app_name != 'Unknown':
            detected_apps.add(app_name)
            
            # PROJECT -> APPLICATION
            if app_name not in [r['destination_node'] for r in records if r['relationship'] == 'contains' and r['source_type'] == 'project']:
                records.append({
                    'source_node': project_name,
                    'source_type': 'project',
                    'destination_node': app_name,
                    'destination_type': 'application',
                    'relationship': 'contains',
                    'file_path': None,
                    'application': app_name
                })
        
        # Handle folder hierarchy
        if rel_root != '.':
            path_parts = rel_root.split(os.sep)
            for i in range(len(path_parts)):
                current_folder = path_parts[i]
                parent = path_parts[i-1] if i > 0 else None
                
                # Determine parent type and name
                if parent is None:
                    # Top-level folder under application
                    if app_name != 'Unknown':
                        parent_name = app_name
                        parent_type = 'application'
                    else:
                        parent_name = project_name
                        parent_type = 'project'
                else:
                    parent_name = parent
                    parent_type = 'folder'
                
                folder_key = f'{parent_name}->{current_folder}'
                if folder_key not in processed_folders:
                    records.append({
                        'source_node': parent_name,
                        'source_type': parent_type,
                        'destination_node': current_folder,
                        'destination_type': 'folder',
                        'relationship': 'contains',
                        'file_path': None,
                        'application': app_name
                    })
                    processed_folders.add(folder_key)
        
        # Handle files
        current_folder = os.path.basename(root) if rel_root != '.' else project_name
        for f in files:
            if f.endswith('.java'):
                file_rel_path = os.path.relpath(os.path.join(root, f), base_path)
                records.append({
                    'source_node': current_folder,
                    'source_type': 'folder' if rel_root != '.' else 'project',
                    'destination_node': f,
                    'destination_type': 'file',
                    'relationship': 'contains',
                    'file_path': file_rel_path,
                    'application': app_name
                })
    
    return records

print('📁 Extracting focused hierarchy...')
hierarchy_records = extract_focused_hierarchy(BASE_PATH)
df_hierarchy = pd.DataFrame(hierarchy_records)

print(f'✅ Stage 1 Complete: {len(df_hierarchy)} hierarchical relationships extracted')
if len(df_hierarchy) > 0:
    print(f'   📊 Relationship breakdown: {df_hierarchy["relationship"].value_counts().to_dict()}')
    print(f'   🏢 Applications: {df_hierarchy["application"].value_counts().to_dict()}')

# ========== STAGE 2: FOCUSED AST EXTRACTION ==========

def read_source_code(file_path):
    '''Read source code file'''
    with open(file_path, 'r', encoding='utf-8') as f:
        return f.read().encode('utf-8')

def extract_focused_ast_structure(file_path):
    '''Extract only the focused nodes and relationships we want'''
    records = []
    source_code = read_source_code(file_path)
    source_code_str = source_code.decode('utf-8')
    tree = parser.parse(source_code)
    root_node = tree.root_node
    file_name = os.path.basename(file_path)
    app_name = detect_application(file_path)
    
    def clean_node_name(name):
        '''Clean node names'''
        if not name:
            return name
        
        prefixes_to_remove = ['method:', 'class:', 'variable:', 'field:']
        for prefix in prefixes_to_remove:
            if name.lower().startswith(prefix):
                name = name[len(prefix):]
        
        name = re.sub(r'\.(java|class)$', '', name, flags=re.IGNORECASE)
        return name.strip()

    def traverse(node, parent_type=None, parent_name=None):
        # Handle class declarations
        if node.type == 'class_declaration':
            class_name = None
            for child in node.children:
                if child.type == 'identifier':
                    class_name = clean_node_name(source_code[child.start_byte:child.end_byte].decode('utf-8'))
                    
                    # FILE -> CLASS relationship
                    records.append({
                        'source_node': file_name,
                        'source_type': 'file',
                        'destination_node': class_name,
                        'destination_type': 'class',
                        'relationship': 'declares',
                        'file_path': str(file_path),
                        'application': app_name
                    })
                    
                    # Extract API endpoints
                    endpoints = extract_api_endpoints(source_code_str)
                    for ep in endpoints:
                        endpoint_name = f"{ep['method']} {ep['path']}"
                        records.append({
                            'source_node': class_name,
                            'source_type': 'class',
                            'destination_node': endpoint_name,
                            'destination_type': 'endpoint',
                            'relationship': 'exposes',
                            'file_path': str(file_path),
                            'application': app_name
                        })
                    
                    # Extract database entities as DATA nodes
                    db_entities = extract_database_entities(source_code_str)
                    for entity in db_entities:
                        records.append({
                            'source_node': class_name,
                            'source_type': 'class',
                            'destination_node': entity['name'],
                            'destination_type': 'data',
                            'relationship': 'data_find',
                            'file_path': str(file_path),
                            'application': app_name
                        })
                    
                    # Extract class inheritance (EXTENDS only, minimal IMPLEMENTS)
                    class_relationships = extract_class_relationships(source_code_str)
                    for rel in class_relationships:
                        if rel['child_class'] == class_name:
                            # Prioritize EXTENDS over IMPLEMENTS
                            if rel['relationship_type'] == 'extends':
                                records.append({
                                    'source_node': class_name,
                                    'source_type': 'class',
                                    'destination_node': rel['parent_class'],
                                    'destination_type': 'class',
                                    'relationship': 'extends',
                                    'file_path': str(file_path),
                                    'application': app_name
                                })
                            # Only include IMPLEMENTS for key interfaces
                            elif rel['relationship_type'] == 'implements' and len(rel['parent_class']) > 8:
                                records.append({
                                    'source_node': class_name,
                                    'source_type': 'class',
                                    'destination_node': rel['parent_class'],
                                    'destination_type': 'interface',
                                    'relationship': 'implements',
                                    'file_path': str(file_path),
                                    'application': app_name
                                })
                    break
            
            # Traverse children with class context
            for child in node.children:
                traverse(child, 'class', class_name)
                
        # Handle method declarations
        elif node.type == 'method_declaration':
            method_name = None
            for child in node.children:
                if child.type == 'identifier':
                    method_name = clean_node_name(source_code[child.start_byte:child.end_byte].decode('utf-8'))
                    
                    if parent_name and parent_type == 'class':
                        records.append({
                            'source_node': parent_name,
                            'source_type': parent_type,
                            'destination_node': method_name,
                            'destination_type': 'method',
                            'relationship': 'declares',
                            'file_path': str(file_path),
                            'application': app_name
                        })
                    break
            
            for child in node.children:
                traverse(child, 'method', method_name)
                
        # Handle field declarations - only meaningful variables
        elif node.type == 'field_declaration':
            for child in node.children:
                if child.type == 'variable_declarator':
                    for grandchild in child.children:
                        if grandchild.type == 'identifier':
                            field_name = clean_node_name(source_code[grandchild.start_byte:grandchild.end_byte].decode('utf-8'))
                            
                            if is_meaningful_variable(field_name) and parent_name and parent_type == 'class':
                                records.append({
                                    'source_node': parent_name,
                                    'source_type': parent_type,
                                    'destination_node': field_name,
                                    'destination_type': 'variable',
                                    'relationship': 'has_field',
                                    'file_path': str(file_path),
                                    'application': app_name
                                })
        else:
            # Continue traversing
            for child in node.children:
                traverse(child, parent_type, parent_name)

    traverse(root_node)
    return records

# Helper functions from original code
def extract_api_endpoints(source_code_str):
    '''Extract REST API endpoints from Spring annotations'''
    endpoints = []
    mapping_patterns = {
        'RequestMapping': [
            r'@RequestMapping\s*\(\s*value\s*=\s*["\']([^"\']+)["\']',
            r'@RequestMapping\s*\(\s*["\']([^"\']+)["\']',
            r'@RequestMapping\s*\(\s*path\s*=\s*["\']([^"\']+)["\']'
        ],
        'GetMapping': [
            r'@GetMapping\s*\(\s*["\']([^"\']+)["\']',
            r'@GetMapping\s*\(\s*value\s*=\s*["\']([^"\']+)["\']'
        ],
        'PostMapping': [
            r'@PostMapping\s*\(\s*["\']([^"\']+)["\']',
            r'@PostMapping\s*\(\s*value\s*=\s*["\']([^"\']+)["\']'
        ]
    }
    
    for mapping_type, patterns in mapping_patterns.items():
        for pattern in patterns:
            matches = re.findall(pattern, source_code_str, re.MULTILINE | re.DOTALL)
            for match in matches:
                if match.strip():
                    endpoints.append({
                        'type': mapping_type,
                        'path': match.strip(),
                        'method': mapping_type.replace('Mapping', '').upper() if mapping_type != 'RequestMapping' else 'GET'
                    })
    
    return endpoints

def extract_database_entities(source_code_str):
    '''Extract database entities from JPA annotations'''
    entities = []
    
    # Entity detection
    if re.search(r'@Entity\s*(?:\([^)]*\))?', source_code_str, re.MULTILINE):
        table_matches = re.findall(r'@Table\s*\(\s*name\s*=\s*["\']([^"\']+)["\']', source_code_str)
        for table_name in table_matches:
            if table_name.strip():
                entities.append({
                    'type': 'table',
                    'name': table_name.strip()
                })
    
    # Repository pattern detection
    repository_pattern = r'interface\s+(\w+)\s+extends\s+.*Repository'
    repo_matches = re.findall(repository_pattern, source_code_str)
    for repo_name in repo_matches:
        entity_name = repo_name.replace('Repository', '').replace('Rep', '')
        if entity_name:
            entities.append({
                'type': 'table',
                'name': entity_name.lower()
            })
    
    return entities

def extract_class_relationships(source_code_str):
    '''Extract class inheritance relationships'''
    relationships = []
    
    # Class extends
    class_extends_pattern = r'class\s+(\w+)\s+extends\s+([\w<>]+)'
    class_matches = re.findall(class_extends_pattern, source_code_str)
    for child_class, parent_class in class_matches:
        parent_class = re.sub(r'<.*?>', '', parent_class).strip()
        if parent_class:
            relationships.append({
                'child_class': child_class,
                'parent_class': parent_class,
                'relationship_type': 'extends'
            })
    
    # Class implements (minimal)
    implements_pattern = r'class\s+(\w+)(?:\s+extends\s+\w+)?\s+implements\s+([\w<>,\s]+)'
    impl_matches = re.findall(implements_pattern, source_code_str)
    for class_name, implements_clause in impl_matches:
        interfaces = [part.strip().split('<')[0].strip() for part in implements_clause.split(',')]
        for interface in interfaces:
            if interface and len(interface) > 8:  # Only meaningful interfaces
                relationships.append({
                    'child_class': class_name,
                    'parent_class': interface,
                    'relationship_type': 'implements'
                })
    
    return relationships

# Execute focused AST extraction
print('🌳 Extracting focused AST structures...')
ast_records = []
java_files = list(BASE_PATH.rglob('*.java'))

for file_path in tqdm(java_files, desc='Focused AST Processing'):
    try:
        ast_records.extend(extract_focused_ast_structure(file_path))
    except Exception as e:
        print(f'❌ Error processing {file_path.name}: {e}')
        continue

df_ast = pd.DataFrame(ast_records)
print(f'✅ Stage 2 Complete: {len(df_ast)} focused relationships extracted')
if len(df_ast) > 0:
    print(f'   📊 Relationship types: {df_ast["relationship"].value_counts().to_dict()}')
    print(f'   🏢 Applications: {df_ast["application"].value_counts().to_dict()}')

# ========== STAGE 3: FOCUSED DATA TRANSFORMATIONS ==========

def extract_focused_data_transformations():
    '''Extract only meaningful data transformations'''
    transformation_records = []
    java_files = list(BASE_PATH.rglob('*.java'))
    
    for file_path in tqdm(java_files, desc='Extracting Data Transformations'):
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                source_code_str = f.read()
            
            app_name = detect_application(file_path)
            class_name = file_path.stem
            
            # Focus on meaningful data transformation patterns
            transformation_patterns = [
                # Service method calls that transform data
                r'(\w{5,})\s*=\s*(\w+Service)\s*\.\s*(\w+)\s*\(',
                # Repository operations
                r'(\w{5,})\s*=\s*(\w+Repository)\s*\.\s*(find|save|delete)\w*\s*\(',
                # Stream transformations (meaningful variables only)
                r'(\w{5,})\s*=\s*(\w{5,})\.stream\s*\(\s*\)\.(?:map|filter|collect)\s*\(',
                # Mapper transformations
                r'(\w{5,})\s*=\s*\w*[Mm]apper\s*\.\s*\w+\s*\(\s*(\w{5,})'
            ]
            
            for pattern in transformation_patterns:
                matches = re.findall(pattern, source_code_str, re.MULTILINE)
                for match in matches:
                    if len(match) >= 2:
                        target_var = match[0]
                        source_var = match[1] if len(match) >= 2 else 'unknown'
                        
                        if (is_meaningful_variable(target_var) and 
                            is_meaningful_variable(source_var) and 
                            target_var != source_var):
                            
                            transformation_records.append({
                                'source_node': source_var,
                                'source_type': 'variable',
                                'destination_node': target_var,
                                'destination_type': 'variable',
                                'relationship': 'transforms_to',
                                'file_path': str(file_path),
                                'application': app_name
                            })
            
            # Extract method-level data operations
            data_operation_patterns = [
                # SQL queries that find data
                r'@Query\s*\(\s*["\']([^"\']*(SELECT|FROM)[^"\']*)["\']',
                # JPA method names that indicate data operations
                r'(find\w+By\w+|save\w+|delete\w+)\s*\('
            ]
            
            for pattern in data_operation_patterns:
                matches = re.findall(pattern, source_code_str, re.MULTILINE | re.IGNORECASE)
                for match in matches:
                    if isinstance(match, tuple):
                        query = match[0]
                        # Extract table names from SQL
                        table_matches = re.findall(r'FROM\s+(\w+)', query, re.IGNORECASE)
                        for table_name in table_matches:
                            transformation_records.append({
                                'source_node': table_name.lower(),
                                'source_type': 'data',
                                'destination_node': class_name,
                                'destination_type': 'class',
                                'relationship': 'data_find',
                                'file_path': str(file_path),
                                'application': app_name
                            })
                    else:
                        # Method name indicates data operation
                        method_name = match
                        if len(method_name) > 8:  # Only meaningful method names
                            transformation_records.append({
                                'source_node': method_name,
                                'source_type': 'method',
                                'destination_node': 'data_operation',
                                'destination_type': 'data',
                                'relationship': 'data_find',
                                'file_path': str(file_path),
                                'application': app_name
                            })
        
        except Exception as e:
            continue
    
    return transformation_records

print('🔄 Extracting focused data transformations...')
transformation_records = extract_focused_data_transformations()
df_transformations = pd.DataFrame(transformation_records)

print(f'✅ Stage 3 Complete: {len(df_transformations)} data transformation relationships extracted')
if len(df_transformations) > 0:
    print(f'   📊 Transformation types: {df_transformations["relationship"].value_counts().to_dict()}')

# ========== STAGE 4: COMBINE AND LOAD TO NEO4J ==========

def combine_focused_data():
    '''Combine all focused data sources'''
    all_records = []
    
    # Add hierarchy records
    if 'df_hierarchy' in globals() and len(df_hierarchy) > 0:
        all_records.extend(df_hierarchy.to_dict('records'))
        print(f'📁 Added {len(df_hierarchy)} hierarchy relationships')
    
    # Add AST records
    if 'df_ast' in globals() and len(df_ast) > 0:
        all_records.extend(df_ast.to_dict('records'))
        print(f'🌳 Added {len(df_ast)} AST relationships')
    
    # Add transformation records
    if 'df_transformations' in globals() and len(df_transformations) > 0:
        all_records.extend(df_transformations.to_dict('records'))
        print(f'🔄 Added {len(df_transformations)} transformation relationships')
    
    return pd.DataFrame(all_records)

def load_focused_to_neo4j(df_final):
    '''Load focused data to Neo4j with proper node types'''
    if graph is None:
        print('❌ Neo4j connection not available')
        return
    
    try:
        # Clear existing data
        print('🧹 Clearing existing data...')
        graph.query('MATCH (n) DETACH DELETE n')
        
        # Create nodes and relationships with proper labels
        print('📊 Loading focused nodes and relationships...')
        
        # Map our node types to Neo4j labels
        node_type_mapping = {
            'project': 'Project',
            'application': 'Application', 
            'folder': 'Folder',
            'file': 'File',
            'class': 'Class',
            'method': 'Method',
            'interface': 'Interface',
            'variable': 'Variable',
            'endpoint': 'Endpoint',
            'data': 'Data'
        }
        
        # Map our relationships to Neo4j relationship types
        relationship_mapping = {
            'contains': 'CONTAINS',
            'declares': 'DECLARES',
            'declares_variable': 'DECLARES_VARIABLE',
            'exposes': 'EXPOSES',
            'extends': 'EXTENDS',
            'implements': 'IMPLEMENTS',
            'has_field': 'HAS_FIELD',
            'uses': 'USES',
            'transforms_to': 'TRANSFORMS_TO',
            'data_find': 'DATA_FIND'
        }
        
        for _, row in tqdm(df_final.iterrows(), desc='Loading to Neo4j', total=len(df_final)):
            try:
                source_label = node_type_mapping.get(row['source_type'], row['source_type'].title())
                target_label = node_type_mapping.get(row['destination_type'], row['destination_type'].title())
                rel_type = relationship_mapping.get(row['relationship'], row['relationship'].upper())
                
                # Create nodes and relationship
                cypher = f"""
                MERGE (source:{source_label} {{name: $source_name, application: $app}})
                MERGE (target:{target_label} {{name: $target_name, application: $app}})
                MERGE (source)-[:{rel_type}]->(target)
                """
                
                graph.query(cypher, {
                    'source_name': str(row['source_node']),
                    'target_name': str(row['destination_node']),
                    'app': str(row['application'])
                })
            
            except Exception as e:
                print(f'⚠️ Error loading relationship: {e}')
                continue
        
        # Create indexes for performance
        indexes = [
            'CREATE INDEX IF NOT EXISTS FOR (n:Project) ON (n.name)',
            'CREATE INDEX IF NOT EXISTS FOR (n:Application) ON (n.name)',
            'CREATE INDEX IF NOT EXISTS FOR (n:Class) ON (n.name)',
            'CREATE INDEX IF NOT EXISTS FOR (n:Method) ON (n.name)',
            'CREATE INDEX IF NOT EXISTS FOR (n:Data) ON (n.name)'
        ]
        
        for index in indexes:
            try:
                graph.query(index)
            except:
                pass
        
        print('✅ Focused data successfully loaded to Neo4j')
        
        # Get statistics
        stats = graph.query("""
        MATCH (n)
        RETURN labels(n)[0] as node_type, count(*) as count
        ORDER BY count DESC
        """)
        
        print('\n📊 Neo4j Node Statistics:')
        for stat in stats:
            print(f'   {stat["node_type"]}: {stat["count"]} nodes')
        
        # Get relationship statistics
        rel_stats = graph.query("""
        MATCH ()-[r]->()
        RETURN type(r) as relationship_type, count(*) as count
        ORDER BY count DESC
        """)
        
        print('\n🔗 Neo4j Relationship Statistics:')
        for stat in rel_stats:
            print(f'   {stat["relationship_type"]}: {stat["count"]} relationships')
    
    except Exception as e:
        print(f'❌ Error loading to Neo4j: {e}')

# Execute data combination and loading
print('\n🔄 Combining all focused data sources...')
df_final = combine_focused_data()

print(f'\n✅ Final focused dataset created:')
print(f'   📊 Total relationships: {len(df_final)}')
if len(df_final) > 0:
    print(f'   🏢 Applications: {df_final["application"].value_counts().to_dict()}')
    print(f'   🔗 Relationship types: {df_final["relationship"].value_counts().to_dict()}')

# Load to Neo4j
print('\n🚀 Loading focused data to Neo4j...')
load_focused_to_neo4j(df_final)