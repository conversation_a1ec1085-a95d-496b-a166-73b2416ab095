{"cells": [{"cell_type": "markdown", "id": "improvements_summary", "metadata": {}, "source": ["# 🚀 Focused Java Code + Data Lineage Analysis v9 - FILTERED VERSION\n", "\n", "## 🎯 Focused Node Types:\n", "\n", "### **Nodes Captured** ✅\n", "- **PROJECT** - Root project node\n", "- **APPLICATIONS** - ServiceBolt, UnifiedBolt applications\n", "- **FOLDERS** - Directory structure\n", "- **FILE** - Java source files\n", "- **CLASS** - Java classes\n", "- **METHOD** - Class methods\n", "- **INTERFACE** - Java interfaces (minimal)\n", "- **VARIABLE** - Meaningful variables only\n", "- **ENDPOINT** - REST API endpoints\n", "- **DATA** - Database tables and data entities\n", "\n", "### **Relationships Captured** ✅\n", "- **CONTAINS** - Hierarchical containment (project->app, folder->file, etc.)\n", "- **DATA_FIND** - Data discovery and retrieval operations\n", "- **DECLARES** - Declaration relationships (file->class, class->method)\n", "- **DECLARES_VARIABLE** - Variable declarations\n", "- **EXPOSES** - API endpoint exposure\n", "- **EXTENDS** - Class inheritance\n", "- **HAS_FIELD** - Field ownership\n", "- **IMPLEMENTS** - Interface implementation (minimal)\n", "- **TRANSFORMS_TO** - Data transformation flows\n", "- **USES** - Usage relationships\n", "\n", "## 🚫 Filtered Out:\n", "- Noise variables (i, j, temp, etc.)\n", "- Overly granular relationships\n", "- Complex variable flows that don't add business value\n", "- Redundant interface relationships\n", "\n", "## 🎯 Focus Areas:\n", "- **Global variable tracking** over local variables\n", "- **State changes** and data transformations\n", "- **Class extends relationships** prioritized over interfaces\n", "- **Business-meaningful connections** only"]}, {"cell_type": "code", "execution_count": 1, "id": "setup", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_10932\\16594514.py:30: LangChainDeprecationWarning: The class `Neo4jGraph` was deprecated in LangChain 0.3.8 and will be removed in 1.0. An updated version of the class exists in the :class:`~langchain-neo4j package and should be used instead. To use it run `pip install -U :class:`~langchain-neo4j` and import as `from :class:`~langchain_neo4j import Neo4jGraph``.\n", "  graph = Neo4jGraph(url=NEO4J_URI, username=NEO4J_USER, password=NEO4J_PASSWORD, database=NEO4J_DB)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Neo4j connection established\n", "🚀 Setup complete! Ready for focused analysis...\n"]}], "source": ["# ========== IMPORTS AND SETUP ==========\n", "import os\n", "from pathlib import Path\n", "from tqdm import tqdm\n", "import pandas as pd\n", "import re\n", "from collections import defaultdict\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "from tree_sitter import Language, Parser\n", "import tree_sitter_java as tsjava\n", "\n", "from langchain_community.document_loaders import TextLoader\n", "from langchain.text_splitter import RecursiveCharacterTextSplitter, Language as LC_Language\n", "from langchain_google_genai import ChatGoogleGenerativeAI\n", "from langchain_experimental.graph_transformers import LLMGraphTransformer\n", "from langchain_community.graphs import Neo4jGraph\n", "\n", "# ========== CONFIGURATION ==========\n", "BASE_PATH = Path(r'C:/Shaik/sample/OneInsights')\n", "NEO4J_URI = 'bolt://localhost:7687'\n", "NEO4J_USER = 'neo4j'\n", "NEO4J_PASSWORD = 'Test@7889'\n", "NEO4J_DB = 'oneinsights-v9'\n", "GOOGLE_API_KEY = 'AIzaSyAeSuntl3dxGqQhwxRG_jom1V_EjxEPSwc'\n", "\n", "# ========== INITIALIZE COMPONENTS ==========\n", "try:\n", "    graph = Neo4jGraph(url=NEO4J_URI, username=NEO4J_USER, password=NEO4J_PASSWORD, database=NEO4J_DB)\n", "    print('✅ Neo4j connection established')\n", "except Exception as e:\n", "    print(f'❌ Neo4j connection failed: {e}')\n", "    graph = None\n", "\n", "JAVA_LANGUAGE = Language(tsjava.language())\n", "parser = Parser(JAVA_LANGUAGE)\n", "\n", "llm = ChatGoogleGenerativeAI(\n", "    model='gemini-2.0-flash-exp',\n", "    temperature=0,\n", "    google_api_key=GOOGLE_API_KEY\n", ")\n", "\n", "# ========== ENHANCED FILTERING FOR FOCUSED ANALYSIS ==========\n", "NOISE_VARIABLES = {\n", "    # Loop variables\n", "    'i', 'j', 'k', 'l', 'm', 'n', 'x', 'y', 'z',\n", "    # Common temporary variables\n", "    'temp', 'tmp', 'obj', 'item', 'elem', 'node', 'val', 'value',\n", "    # Common short variables\n", "    'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w',\n", "    # Reserved keywords\n", "    'this', 'super', 'null', 'true', 'false', 'void', 'return',\n", "    # Meaningless short names\n", "    'it', 'ex', 'e1', 'e2', 'o1', 'o2'\n", "}\n", "\n", "APPLICATIONS = {\n", "    'ServiceBolt': 'REST API Service Layer',\n", "    'UnifiedBolt': 'Core Business Logic and Data Layer'\n", "}\n", "\n", "def is_meaningful_variable(var_name):\n", "    '''Filter out noise variables and keep only meaningful ones'''\n", "    if not var_name or len(var_name) < 2:\n", "        return False\n", "    \n", "    if var_name.lower() in NOISE_VARIABLES:\n", "        return False\n", "    \n", "    # Focus on business-meaningful variables\n", "    if len(var_name) > 4:  # Increased threshold for more meaningful names\n", "        return True\n", "    \n", "    meaningful_short = {'id', 'url', 'api', 'key', 'dto', 'dao', 'req', 'res', 'ctx', 'data'}\n", "    return var_name.lower() in meaningful_short\n", "\n", "def detect_application(file_path):\n", "    '''Detect which application a file belongs to'''\n", "    path_str = str(file_path).replace('\\\\', '/')\n", "    if 'ServiceBolt' in path_str:\n", "        return 'ServiceBolt'\n", "    elif 'UnifiedBolt' in path_str:\n", "        return 'UnifiedBolt'\n", "    return 'Unknown'\n", "\n", "print('🚀 Setup complete! Ready for focused analysis...')"]}, {"cell_type": "code", "execution_count": 2, "id": "folder_hierarchy", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["📁 Extracting focused hierarchy...\n", "✅ Stage 1 Complete: 29 hierarchical relationships extracted\n", "   📊 Relationship breakdown: {'contains': 29}\n", "   🏢 Applications: {'UnifiedBolt': 22, 'ServiceBolt': 7}\n"]}], "source": ["# ========== STAGE 1: PROJECT + APPLICATION + FOLDER + FILE HIERARCHY ==========\n", "\n", "def extract_focused_hierarchy(base_path):\n", "    '''Extract focused hierarchy: PROJECT -> APPLICATIONS -> FOLDERS -> FILES'''\n", "    records = []\n", "    base_path = os.path.abspath(base_path)\n", "    project_name = os.path.basename(base_path)\n", "    processed_folders = set()\n", "    detected_apps = set()\n", "\n", "    # Create PROJECT node (root)\n", "    for root, dirs, files in os.walk(base_path):\n", "        rel_root = os.path.relpath(root, base_path)\n", "        app_name = detect_application(root)\n", "        \n", "        if app_name != 'Unknown':\n", "            detected_apps.add(app_name)\n", "            \n", "            # PROJECT -> APPLICATION\n", "            if app_name not in [r['destination_node'] for r in records if r['relationship'] == 'contains' and r['source_type'] == 'project']:\n", "                records.append({\n", "                    'source_node': project_name,\n", "                    'source_type': 'project',\n", "                    'destination_node': app_name,\n", "                    'destination_type': 'application',\n", "                    'relationship': 'contains',\n", "                    'file_path': None,\n", "                    'application': app_name\n", "                })\n", "        \n", "        # Handle folder hierarchy\n", "        if rel_root != '.':\n", "            path_parts = rel_root.split(os.sep)\n", "            for i in range(len(path_parts)):\n", "                current_folder = path_parts[i]\n", "                parent = path_parts[i-1] if i > 0 else None\n", "                \n", "                # Determine parent type and name\n", "                if parent is None:\n", "                    # Top-level folder under application\n", "                    if app_name != 'Unknown':\n", "                        parent_name = app_name\n", "                        parent_type = 'application'\n", "                    else:\n", "                        parent_name = project_name\n", "                        parent_type = 'project'\n", "                else:\n", "                    parent_name = parent\n", "                    parent_type = 'folder'\n", "                \n", "                folder_key = f'{parent_name}->{current_folder}'\n", "                if folder_key not in processed_folders:\n", "                    records.append({\n", "                        'source_node': parent_name,\n", "                        'source_type': parent_type,\n", "                        'destination_node': current_folder,\n", "                        'destination_type': 'folder',\n", "                        'relationship': 'contains',\n", "                        'file_path': None,\n", "                        'application': app_name\n", "                    })\n", "                    processed_folders.add(folder_key)\n", "        \n", "        # Handle files\n", "        current_folder = os.path.basename(root) if rel_root != '.' else project_name\n", "        for f in files:\n", "            if f.endswith('.java'):\n", "                file_rel_path = os.path.relpath(os.path.join(root, f), base_path)\n", "                records.append({\n", "                    'source_node': current_folder,\n", "                    'source_type': 'folder' if rel_root != '.' else 'project',\n", "                    'destination_node': f,\n", "                    'destination_type': 'file',\n", "                    'relationship': 'contains',\n", "                    'file_path': file_rel_path,\n", "                    'application': app_name\n", "                })\n", "    \n", "    return records\n", "\n", "print('📁 Extracting focused hierarchy...')\n", "hierarchy_records = extract_focused_hierarchy(BASE_PATH)\n", "df_hierarchy = pd.DataFrame(hierarchy_records)\n", "\n", "print(f'✅ Stage 1 Complete: {len(df_hierarchy)} hierarchical relationships extracted')\n", "if len(df_hierarchy) > 0:\n", "    print(f'   📊 Relationship breakdown: {df_hierarchy[\"relationship\"].value_counts().to_dict()}')\n", "    print(f'   🏢 Applications: {df_hierarchy[\"application\"].value_counts().to_dict()}')"]}, {"cell_type": "code", "execution_count": 3, "id": "focused_ast_extraction", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🌳 Extracting focused AST structures...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Focused AST Processing: 100%|██████████| 19/19 [00:00<00:00, 52.64it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Stage 2 Complete: 313 focused relationships extracted\n", "   📊 Relationship types: {'declares': 190, 'has_field': 107, 'exposes': 10, 'implements': 3, 'extends': 3}\n", "   🏢 Applications: {'UnifiedBolt': 270, 'ServiceBolt': 43}\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["# ========== STAGE 2: FOCUSED AST EXTRACTION ==========\n", "\n", "def read_source_code(file_path):\n", "    '''Read source code file'''\n", "    with open(file_path, 'r', encoding='utf-8') as f:\n", "        return f.read().encode('utf-8')\n", "\n", "def extract_focused_ast_structure(file_path):\n", "    '''Extract only the focused nodes and relationships we want'''\n", "    records = []\n", "    source_code = read_source_code(file_path)\n", "    source_code_str = source_code.decode('utf-8')\n", "    tree = parser.parse(source_code)\n", "    root_node = tree.root_node\n", "    file_name = os.path.basename(file_path)\n", "    app_name = detect_application(file_path)\n", "    \n", "    def clean_node_name(name):\n", "        '''Clean node names'''\n", "        if not name:\n", "            return name\n", "        \n", "        prefixes_to_remove = ['method:', 'class:', 'variable:', 'field:']\n", "        for prefix in prefixes_to_remove:\n", "            if name.lower().startswith(prefix):\n", "                name = name[len(prefix):]\n", "        \n", "        name = re.sub(r'\\.(java|class)$', '', name, flags=re.IGNORECASE)\n", "        return name.strip()\n", "\n", "    def traverse(node, parent_type=None, parent_name=None):\n", "        # Handle class declarations\n", "        if node.type == 'class_declaration':\n", "            class_name = None\n", "            for child in node.children:\n", "                if child.type == 'identifier':\n", "                    class_name = clean_node_name(source_code[child.start_byte:child.end_byte].decode('utf-8'))\n", "                    \n", "                    # FILE -> CLASS relationship\n", "                    records.append({\n", "                        'source_node': file_name,\n", "                        'source_type': 'file',\n", "                        'destination_node': class_name,\n", "                        'destination_type': 'class',\n", "                        'relationship': 'declares',\n", "                        'file_path': str(file_path),\n", "                        'application': app_name\n", "                    })\n", "                    \n", "                    # Extract API endpoints\n", "                    endpoints = extract_api_endpoints(source_code_str)\n", "                    for ep in endpoints:\n", "                        endpoint_name = f\"{ep['method']} {ep['path']}\"\n", "                        records.append({\n", "                            'source_node': class_name,\n", "                            'source_type': 'class',\n", "                            'destination_node': endpoint_name,\n", "                            'destination_type': 'endpoint',\n", "                            'relationship': 'exposes',\n", "                            'file_path': str(file_path),\n", "                            'application': app_name\n", "                        })\n", "                    \n", "                    # Extract database entities as DATA nodes\n", "                    db_entities = extract_database_entities(source_code_str)\n", "                    for entity in db_entities:\n", "                        records.append({\n", "                            'source_node': class_name,\n", "                            'source_type': 'class',\n", "                            'destination_node': entity['name'],\n", "                            'destination_type': 'data',\n", "                            'relationship': 'data_find',\n", "                            'file_path': str(file_path),\n", "                            'application': app_name\n", "                        })\n", "                    \n", "                    # Extract class inheritance (EXTENDS only, minimal IMPLEMENTS)\n", "                    class_relationships = extract_class_relationships(source_code_str)\n", "                    for rel in class_relationships:\n", "                        if rel['child_class'] == class_name:\n", "                            # Prioritize EXTENDS over IMPLEMENTS\n", "                            if rel['relationship_type'] == 'extends':\n", "                                records.append({\n", "                                    'source_node': class_name,\n", "                                    'source_type': 'class',\n", "                                    'destination_node': rel['parent_class'],\n", "                                    'destination_type': 'class',\n", "                                    'relationship': 'extends',\n", "                                    'file_path': str(file_path),\n", "                                    'application': app_name\n", "                                })\n", "                            # Only include IMPLEMENTS for key interfaces\n", "                            elif rel['relationship_type'] == 'implements' and len(rel['parent_class']) > 8:\n", "                                records.append({\n", "                                    'source_node': class_name,\n", "                                    'source_type': 'class',\n", "                                    'destination_node': rel['parent_class'],\n", "                                    'destination_type': 'interface',\n", "                                    'relationship': 'implements',\n", "                                    'file_path': str(file_path),\n", "                                    'application': app_name\n", "                                })\n", "                    break\n", "            \n", "            # Traverse children with class context\n", "            for child in node.children:\n", "                traverse(child, 'class', class_name)\n", "                \n", "        # Handle method declarations\n", "        elif node.type == 'method_declaration':\n", "            method_name = None\n", "            for child in node.children:\n", "                if child.type == 'identifier':\n", "                    method_name = clean_node_name(source_code[child.start_byte:child.end_byte].decode('utf-8'))\n", "                    \n", "                    if parent_name and parent_type == 'class':\n", "                        records.append({\n", "                            'source_node': parent_name,\n", "                            'source_type': parent_type,\n", "                            'destination_node': method_name,\n", "                            'destination_type': 'method',\n", "                            'relationship': 'declares',\n", "                            'file_path': str(file_path),\n", "                            'application': app_name\n", "                        })\n", "                    break\n", "            \n", "            for child in node.children:\n", "                traverse(child, 'method', method_name)\n", "                \n", "        # Handle field declarations - only meaningful variables\n", "        elif node.type == 'field_declaration':\n", "            for child in node.children:\n", "                if child.type == 'variable_declarator':\n", "                    for grandchild in child.children:\n", "                        if grandchild.type == 'identifier':\n", "                            field_name = clean_node_name(source_code[grandchild.start_byte:grandchild.end_byte].decode('utf-8'))\n", "                            \n", "                            if is_meaningful_variable(field_name) and parent_name and parent_type == 'class':\n", "                                records.append({\n", "                                    'source_node': parent_name,\n", "                                    'source_type': parent_type,\n", "                                    'destination_node': field_name,\n", "                                    'destination_type': 'variable',\n", "                                    'relationship': 'has_field',\n", "                                    'file_path': str(file_path),\n", "                                    'application': app_name\n", "                                })\n", "        else:\n", "            # Continue traversing\n", "            for child in node.children:\n", "                traverse(child, parent_type, parent_name)\n", "\n", "    traverse(root_node)\n", "    return records\n", "\n", "# Helper functions from original code\n", "def extract_api_endpoints(source_code_str):\n", "    '''Extract REST API endpoints from Spring annotations'''\n", "    endpoints = []\n", "    mapping_patterns = {\n", "        'RequestMapping': [\n", "            r'@RequestMapping\\s*\\(\\s*value\\s*=\\s*[\"\\']([^\"\\']+)[\"\\']',\n", "            r'@RequestMapping\\s*\\(\\s*[\"\\']([^\"\\']+)[\"\\']',\n", "            r'@RequestMapping\\s*\\(\\s*path\\s*=\\s*[\"\\']([^\"\\']+)[\"\\']'\n", "        ],\n", "        'GetMapping': [\n", "            r'@GetMapping\\s*\\(\\s*[\"\\']([^\"\\']+)[\"\\']',\n", "            r'@GetMapping\\s*\\(\\s*value\\s*=\\s*[\"\\']([^\"\\']+)[\"\\']'\n", "        ],\n", "        'PostMapping': [\n", "            r'@PostMapping\\s*\\(\\s*[\"\\']([^\"\\']+)[\"\\']',\n", "            r'@PostMapping\\s*\\(\\s*value\\s*=\\s*[\"\\']([^\"\\']+)[\"\\']'\n", "        ]\n", "    }\n", "    \n", "    for mapping_type, patterns in mapping_patterns.items():\n", "        for pattern in patterns:\n", "            matches = re.findall(pattern, source_code_str, re.MULTILINE | re.DOTALL)\n", "            for match in matches:\n", "                if match.strip():\n", "                    endpoints.append({\n", "                        'type': mapping_type,\n", "                        'path': match.strip(),\n", "                        'method': mapping_type.replace('Mapping', '').upper() if mapping_type != 'RequestMapping' else 'GET'\n", "                    })\n", "    \n", "    return endpoints\n", "\n", "def extract_database_entities(source_code_str):\n", "    '''Extract database entities from JPA annotations'''\n", "    entities = []\n", "    \n", "    # Entity detection\n", "    if re.search(r'@Entity\\s*(?:\\([^)]*\\))?', source_code_str, re.MULTILINE):\n", "        table_matches = re.findall(r'@Table\\s*\\(\\s*name\\s*=\\s*[\"\\']([^\"\\']+)[\"\\']', source_code_str)\n", "        for table_name in table_matches:\n", "            if table_name.strip():\n", "                entities.append({\n", "                    'type': 'table',\n", "                    'name': table_name.strip()\n", "                })\n", "    \n", "    # Repository pattern detection\n", "    repository_pattern = r'interface\\s+(\\w+)\\s+extends\\s+.*Repository'\n", "    repo_matches = re.findall(repository_pattern, source_code_str)\n", "    for repo_name in repo_matches:\n", "        entity_name = repo_name.replace('Repository', '').replace('Rep', '')\n", "        if entity_name:\n", "            entities.append({\n", "                'type': 'table',\n", "                'name': entity_name.lower()\n", "            })\n", "    \n", "    return entities\n", "\n", "def extract_class_relationships(source_code_str):\n", "    '''Extract class inheritance relationships'''\n", "    relationships = []\n", "    \n", "    # Class extends\n", "    class_extends_pattern = r'class\\s+(\\w+)\\s+extends\\s+([\\w<>]+)'\n", "    class_matches = re.findall(class_extends_pattern, source_code_str)\n", "    for child_class, parent_class in class_matches:\n", "        parent_class = re.sub(r'<.*?>', '', parent_class).strip()\n", "        if parent_class:\n", "            relationships.append({\n", "                'child_class': child_class,\n", "                'parent_class': parent_class,\n", "                'relationship_type': 'extends'\n", "            })\n", "    \n", "    # Class implements (minimal)\n", "    implements_pattern = r'class\\s+(\\w+)(?:\\s+extends\\s+\\w+)?\\s+implements\\s+([\\w<>,\\s]+)'\n", "    impl_matches = re.findall(implements_pattern, source_code_str)\n", "    for class_name, implements_clause in impl_matches:\n", "        interfaces = [part.strip().split('<')[0].strip() for part in implements_clause.split(',')]\n", "        for interface in interfaces:\n", "            if interface and len(interface) > 8:  # Only meaningful interfaces\n", "                relationships.append({\n", "                    'child_class': class_name,\n", "                    'parent_class': interface,\n", "                    'relationship_type': 'implements'\n", "                })\n", "    \n", "    return relationships\n", "\n", "# Execute focused AST extraction\n", "print('🌳 Extracting focused AST structures...')\n", "ast_records = []\n", "java_files = list(BASE_PATH.rglob('*.java'))\n", "\n", "for file_path in tqdm(java_files, desc='Focused AST Processing'):\n", "    try:\n", "        ast_records.extend(extract_focused_ast_structure(file_path))\n", "    except Exception as e:\n", "        print(f'❌ Error processing {file_path.name}: {e}')\n", "        continue\n", "\n", "df_ast = pd.DataFrame(ast_records)\n", "print(f'✅ Stage 2 Complete: {len(df_ast)} focused relationships extracted')\n", "if len(df_ast) > 0:\n", "    print(f'   📊 Relationship types: {df_ast[\"relationship\"].value_counts().to_dict()}')\n", "    print(f'   🏢 Applications: {df_ast[\"application\"].value_counts().to_dict()}')"]}, {"cell_type": "code", "execution_count": 4, "id": "focused_data_transformations", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔄 Extracting focused data transformations...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Extracting Data Transformations: 100%|██████████| 19/19 [00:00<00:00, 356.70it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Stage 3 Complete: 17 data transformation relationships extracted\n", "   📊 Transformation types: {'data_find': 9, 'transforms_to': 8}\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["# ========== STAGE 3: FOCUSED DATA TRANSFORMATIONS ==========\n", "\n", "def extract_focused_data_transformations():\n", "    '''Extract only meaningful data transformations'''\n", "    transformation_records = []\n", "    java_files = list(BASE_PATH.rglob('*.java'))\n", "    \n", "    for file_path in tqdm(java_files, desc='Extracting Data Transformations'):\n", "        try:\n", "            with open(file_path, 'r', encoding='utf-8') as f:\n", "                source_code_str = f.read()\n", "            \n", "            app_name = detect_application(file_path)\n", "            class_name = file_path.stem\n", "            \n", "            # Focus on meaningful data transformation patterns\n", "            transformation_patterns = [\n", "                # Service method calls that transform data\n", "                r'(\\w{5,})\\s*=\\s*(\\w+Service)\\s*\\.\\s*(\\w+)\\s*\\(',\n", "                # Repository operations\n", "                r'(\\w{5,})\\s*=\\s*(\\w+Repository)\\s*\\.\\s*(find|save|delete)\\w*\\s*\\(',\n", "                # Stream transformations (meaningful variables only)\n", "                r'(\\w{5,})\\s*=\\s*(\\w{5,})\\.stream\\s*\\(\\s*\\)\\.(?:map|filter|collect)\\s*\\(',\n", "                # Mapper transformations\n", "                r'(\\w{5,})\\s*=\\s*\\w*[Mm]apper\\s*\\.\\s*\\w+\\s*\\(\\s*(\\w{5,})'\n", "            ]\n", "            \n", "            for pattern in transformation_patterns:\n", "                matches = re.findall(pattern, source_code_str, re.MULTILINE)\n", "                for match in matches:\n", "                    if len(match) >= 2:\n", "                        target_var = match[0]\n", "                        source_var = match[1] if len(match) >= 2 else 'unknown'\n", "                        \n", "                        if (is_meaningful_variable(target_var) and \n", "                            is_meaningful_variable(source_var) and \n", "                            target_var != source_var):\n", "                            \n", "                            transformation_records.append({\n", "                                'source_node': source_var,\n", "                                'source_type': 'variable',\n", "                                'destination_node': target_var,\n", "                                'destination_type': 'variable',\n", "                                'relationship': 'transforms_to',\n", "                                'file_path': str(file_path),\n", "                                'application': app_name\n", "                            })\n", "            \n", "            # Extract method-level data operations\n", "            data_operation_patterns = [\n", "                # SQL queries that find data\n", "                r'@Query\\s*\\(\\s*[\"\\']([^\"\\']*(SELECT|FROM)[^\"\\']*)[\"\\']',\n", "                # JPA method names that indicate data operations\n", "                r'(find\\w+By\\w+|save\\w+|delete\\w+)\\s*\\('\n", "            ]\n", "            \n", "            for pattern in data_operation_patterns:\n", "                matches = re.findall(pattern, source_code_str, re.MULTILINE | re.IGNORECASE)\n", "                for match in matches:\n", "                    if isinstance(match, tuple):\n", "                        query = match[0]\n", "                        # Extract table names from SQL\n", "                        table_matches = re.findall(r'FROM\\s+(\\w+)', query, re.IGNORECASE)\n", "                        for table_name in table_matches:\n", "                            transformation_records.append({\n", "                                'source_node': table_name.lower(),\n", "                                'source_type': 'data',\n", "                                'destination_node': class_name,\n", "                                'destination_type': 'class',\n", "                                'relationship': 'data_find',\n", "                                'file_path': str(file_path),\n", "                                'application': app_name\n", "                            })\n", "                    else:\n", "                        # Method name indicates data operation\n", "                        method_name = match\n", "                        if len(method_name) > 8:  # Only meaningful method names\n", "                            transformation_records.append({\n", "                                'source_node': method_name,\n", "                                'source_type': 'method',\n", "                                'destination_node': 'data_operation',\n", "                                'destination_type': 'data',\n", "                                'relationship': 'data_find',\n", "                                'file_path': str(file_path),\n", "                                'application': app_name\n", "                            })\n", "        \n", "        except Exception as e:\n", "            continue\n", "    \n", "    return transformation_records\n", "\n", "print('🔄 Extracting focused data transformations...')\n", "transformation_records = extract_focused_data_transformations()\n", "df_transformations = pd.DataFrame(transformation_records)\n", "\n", "print(f'✅ Stage 3 Complete: {len(df_transformations)} data transformation relationships extracted')\n", "if len(df_transformations) > 0:\n", "    print(f'   📊 Transformation types: {df_transformations[\"relationship\"].value_counts().to_dict()}')"]}, {"cell_type": "code", "execution_count": 5, "id": "combine_and_load", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "🔄 Combining all focused data sources...\n", "📁 Added 29 hierarchy relationships\n", "🌳 Added 313 AST relationships\n", "🔄 Added 17 transformation relationships\n", "\n", "✅ Final focused dataset created:\n", "   📊 Total relationships: 359\n", "   🏢 Applications: {'UnifiedBolt': 299, 'ServiceBolt': 60}\n", "   🔗 Relationship types: {'declares': 190, 'has_field': 107, 'contains': 29, 'exposes': 10, 'data_find': 9, 'transforms_to': 8, 'extends': 3, 'implements': 3}\n", "\n", "🚀 Loading focused data to Neo4j...\n", "🧹 Clearing existing data...\n", "📊 Loading focused nodes and relationships...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Loading to Neo4j: 100%|██████████| 359/359 [00:06<00:00, 58.82it/s] \n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Focused data successfully loaded to Neo4j\n", "\n", "📊 Neo4j Node Statistics:\n", "   Method: 157 nodes\n", "   Variable: 90 nodes\n", "   File: 19 nodes\n", "   Class: 14 nodes\n", "   Endpoint: 10 nodes\n", "   Folder: 8 nodes\n", "   Interface: 3 nodes\n", "   Project: 2 nodes\n", "   Application: 2 nodes\n", "   Data: 2 nodes\n", "\n", "🔗 Neo4j Relationship Statistics:\n", "   DECLARES: 186 relationships\n", "   HAS_FIELD: 107 relationships\n", "   CONTAINS: 29 relationships\n", "   EXPOSES: 10 relationships\n", "   DATA_FIND: 8 relationships\n", "   IMPLEMENTS: 3 relationships\n", "   EXTENDS: 3 relationships\n", "   TRANSFORMS_TO: 1 relationships\n"]}], "source": ["# ========== STAGE 4: COMBINE AND LOAD TO NEO4J ==========\n", "\n", "def combine_focused_data():\n", "    '''Combine all focused data sources'''\n", "    all_records = []\n", "    \n", "    # Add hierarchy records\n", "    if 'df_hierarchy' in globals() and len(df_hierarchy) > 0:\n", "        all_records.extend(df_hierarchy.to_dict('records'))\n", "        print(f'📁 Added {len(df_hierarchy)} hierarchy relationships')\n", "    \n", "    # Add AST records\n", "    if 'df_ast' in globals() and len(df_ast) > 0:\n", "        all_records.extend(df_ast.to_dict('records'))\n", "        print(f'🌳 Added {len(df_ast)} AST relationships')\n", "    \n", "    # Add transformation records\n", "    if 'df_transformations' in globals() and len(df_transformations) > 0:\n", "        all_records.extend(df_transformations.to_dict('records'))\n", "        print(f'🔄 Added {len(df_transformations)} transformation relationships')\n", "    \n", "    return pd.DataFrame(all_records)\n", "\n", "def load_focused_to_neo4j(df_final):\n", "    '''Load focused data to Neo4j with proper node types'''\n", "    if graph is None:\n", "        print('❌ Neo4j connection not available')\n", "        return\n", "    \n", "    try:\n", "        # Clear existing data\n", "        print('🧹 Clearing existing data...')\n", "        graph.query('MATCH (n) DETACH DELETE n')\n", "        \n", "        # Create nodes and relationships with proper labels\n", "        print('📊 Loading focused nodes and relationships...')\n", "        \n", "        # Map our node types to Neo4j labels\n", "        node_type_mapping = {\n", "            'project': 'Project',\n", "            'application': 'Application', \n", "            'folder': 'Folder',\n", "            'file': 'File',\n", "            'class': 'Class',\n", "            'method': 'Method',\n", "            'interface': 'Interface',\n", "            'variable': 'Variable',\n", "            'endpoint': 'Endpoint',\n", "            'data': 'Data'\n", "        }\n", "        \n", "        # Map our relationships to Neo4j relationship types\n", "        relationship_mapping = {\n", "            'contains': 'CONTAINS',\n", "            'declares': 'DECLARES',\n", "            'declares_variable': 'DECLARES_VARIABLE',\n", "            'exposes': 'EXPOSES',\n", "            'extends': 'EXTENDS',\n", "            'implements': 'IMPLEMENTS',\n", "            'has_field': 'HAS_FIELD',\n", "            'uses': 'USES',\n", "            'transforms_to': 'TRANSFORMS_TO',\n", "            'data_find': 'DATA_FIND'\n", "        }\n", "        \n", "        for _, row in tqdm(df_final.iterrows(), desc='Loading to Neo4j', total=len(df_final)):\n", "            try:\n", "                source_label = node_type_mapping.get(row['source_type'], row['source_type'].title())\n", "                target_label = node_type_mapping.get(row['destination_type'], row['destination_type'].title())\n", "                rel_type = relationship_mapping.get(row['relationship'], row['relationship'].upper())\n", "                \n", "                # Create nodes and relationship\n", "                cypher = f\"\"\"\n", "                MERGE (source:{source_label} {{name: $source_name, application: $app}})\n", "                MERGE (target:{target_label} {{name: $target_name, application: $app}})\n", "                MERGE (source)-[:{rel_type}]->(target)\n", "                \"\"\"\n", "                \n", "                graph.query(cypher, {\n", "                    'source_name': str(row['source_node']),\n", "                    'target_name': str(row['destination_node']),\n", "                    'app': str(row['application'])\n", "                })\n", "            \n", "            except Exception as e:\n", "                print(f'⚠️ Error loading relationship: {e}')\n", "                continue\n", "        \n", "        # Create indexes for performance\n", "        indexes = [\n", "            'CREATE INDEX IF NOT EXISTS FOR (n:Project) ON (n.name)',\n", "            'CREATE INDEX IF NOT EXISTS FOR (n:Application) ON (n.name)',\n", "            'CREATE INDEX IF NOT EXISTS FOR (n:Class) ON (n.name)',\n", "            'CREATE INDEX IF NOT EXISTS FOR (n:Method) ON (n.name)',\n", "            'CREATE INDEX IF NOT EXISTS FOR (n:Data) ON (n.name)'\n", "        ]\n", "        \n", "        for index in indexes:\n", "            try:\n", "                graph.query(index)\n", "            except:\n", "                pass\n", "        \n", "        print('✅ Focused data successfully loaded to Neo4j')\n", "        \n", "        # Get statistics\n", "        stats = graph.query(\"\"\"\n", "        MATCH (n)\n", "        RETURN labels(n)[0] as node_type, count(*) as count\n", "        ORDER BY count DESC\n", "        \"\"\")\n", "        \n", "        print('\\n📊 Neo4j Node Statistics:')\n", "        for stat in stats:\n", "            print(f'   {stat[\"node_type\"]}: {stat[\"count\"]} nodes')\n", "        \n", "        # Get relationship statistics\n", "        rel_stats = graph.query(\"\"\"\n", "        MATCH ()-[r]->()\n", "        RETURN type(r) as relationship_type, count(*) as count\n", "        ORDER BY count DESC\n", "        \"\"\")\n", "        \n", "        print('\\n🔗 Neo4j Relationship Statistics:')\n", "        for stat in rel_stats:\n", "            print(f'   {stat[\"relationship_type\"]}: {stat[\"count\"]} relationships')\n", "    \n", "    except Exception as e:\n", "        print(f'❌ Error loading to Neo4j: {e}')\n", "\n", "# Execute data combination and loading\n", "print('\\n🔄 Combining all focused data sources...')\n", "df_final = combine_focused_data()\n", "\n", "print(f'\\n✅ Final focused dataset created:')\n", "print(f'   📊 Total relationships: {len(df_final)}')\n", "if len(df_final) > 0:\n", "    print(f'   🏢 Applications: {df_final[\"application\"].value_counts().to_dict()}')\n", "    print(f'   🔗 Relationship types: {df_final[\"relationship\"].value_counts().to_dict()}')\n", "\n", "# Load to Neo4j\n", "print('\\n🚀 Loading focused data to Neo4j...')\n", "load_focused_to_neo4j(df_final)"]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.8"}}, "nbformat": 4, "nbformat_minor": 5}